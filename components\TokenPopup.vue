<template>
    <u-popup
        :show="show" 
        mode="center" 
        @close="onClose" 
        round="8" 
        :closeable="true"
        :safeAreaInsetBottom="false"
        :overlayStyle="{
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(8px)',
        }"
        :customStyle="{
            width: '90%',
        }"
    >
        <view class="modal-content">
            <view class="modal-title">登录令牌</view>
            <view class="token">{{ token }}</view>
            <button
                class="default-btn"
                hover-class="default-btn-hover"
                type="primary"
                :loading="isRefreshing"
                :disabled="cooldownTime > 0"
                @click="onRefresh"
            >{{ cooldownTime > 0 ? `${cooldownTime}秒重新获取` : '重新获取' }}</button>
        </view>
    </u-popup>
</template>

<script>
export default {
    name: 'TokenPopup',
    
    props: {
        show: {
            type: Boolean,
            default: false
        },
        token: {
            type: String,
            default: ''
        },
        isRefreshing: {
            type: Boolean,
            default: false
        },
        cooldownTime: {
            type: Number,
            default: 0
        }
    },

    emits: ['update:show', 'refresh', 'close'],

    methods: {
        onClose() {
            this.$emit('update:show', false)
            this.$emit('close')
        },
        
        onRefresh() {
            this.$emit('refresh')
        }
    }
}
</script>

<style lang="scss" scoped>
.modal-content {
    background: linear-gradient(135deg, #2a475e, var(--primary-color));
    padding: 60rpx;
    border-radius: 8rpx;
    text-align: center;
    position: relative;
    border: 1px solid rgba(102, 192, 244, 0.2);
    box-shadow: 0 0 80rpx rgba(0, 0, 0, 0.5);
}

.modal-title {
    color: var(--text-primary);
    font-size: 32rpx;
    margin-bottom: 40rpx;
    text-transform: uppercase;
    letter-spacing: 1rpx;
    text-shadow: 0 0 20rpx rgba(102, 192, 244, 0.3);
    position: relative;
    display: inline-block;
    font-weight: 500;

    &::after {
        content: '';
        position: absolute;
        bottom: -12rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: var(--accent-color);
        border-radius: 2rpx;
        box-shadow: 0 0 20rpx rgba(102, 192, 244, 0.5);
    }
}

.token {
    font-size: 92rpx;
    font-weight: bold;
    color: var(--accent-color);
    margin: 50rpx 0;
    letter-spacing: 12rpx;
    text-shadow: 0 0 40rpx rgba(102, 192, 244, 0.5);
    animation: glow 1.5s ease-in-out infinite alternate;
    margin-bottom: 40rpx;
}

@keyframes glow {
    from {
        text-shadow: 0 0 20rpx rgba(102, 192, 244, 0.5);
    }

    to {
        text-shadow: 0 0 40rpx rgba(102, 192, 244, 0.8);
    }
}

.default-btn {
    border-radius: 4px;
    border: none;
    background: linear-gradient(to right, var(--accent-color), var(--accent-hover));
    height: 90rpx;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    
    &:disabled {
        background: linear-gradient(to right, #7a9cb6, #5c8cb0);
        opacity: 0.7;
        color: rgba(255, 255, 255, 0.8);
    }
}
.default-btn-hover {
    background: linear-gradient(to right, #4d91b8, #1476cc);
    transform: translateY(2rpx);
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    opacity: 0.9;
    transition: all 0.2s ease;
}
</style> 