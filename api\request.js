import axios from 'axios-miniprogram';

let instance = axios.create({
	timeout: 3600 * 1000,
	withCredentials: false
})
instance.interceptors.request.use(
  function(config) {
    // console.log('instance.interceptors.request==========', config);
    return config;
  },
  function(error) {
    return Promise.reject(error)
  }
);

instance.interceptors.response.use(
  function(response) {
    console.log('response', response)
    return Promise.reject(response)
  },
  function(error) {
    console.log(error, error)
    return Promise.reject(error)
  }
);

export default instance
