'use strict';
const db = uniCloud.database();
const axios = require('axios');

// 创建预配置的axios实例
const steamAxios = axios.create({
  timeout: 10000, // 10秒超时
  headers: {
    'Content-Type': 'application/json'
  }
});

const zuhaowanAxios = axios.create({
  timeout: 10000, // 10秒超时
  headers: {
    'Content-Type': 'application/json'
  }
});

// 应用配置常量 - 使用驼峰命名和模块化分组
const appConfig = {
  // Steam 相关配置
  steam: {
    apiUrl: 'https://asf.uyizu.com:10010/Api/Bot',
    password: 'Lilong721520.',
    timeout: 10000
  },
  
  // 租号玩平台配置
  zuhaowan: {
    baseUrl: 'https://zu.zuhaowan.com/api',
    serviceName: 'zuhaowan',
    timeout: 10000,
    loginParams: {
      username: 'looong',
      password: '5201230.',
      type: 2
    },
    endpoints: {
      login: '/Login/login',
      orderList: '/order/rentedOutOrderList'
    }
  },
  
  // 订单管理配置
  order: {
    // 刷新策略
    refresh: {
      // 距离过期多长时间内需要刷新（毫秒）- 1小时
      threshold: 1 * 60 * 60 * 1000,
      // 最小刷新间隔（毫秒）- 1分钟，避免频繁请求
      minInterval: 1 * 60 * 1000
    },
    // 查询配置
    query: {
      pageSize: 50,
      requiredFields: {
        _id: true,
        order_num: true,
        jsm: true,
        order_status: true,
        end_time: true,
        unlock_code: true,
        update_time: true,
        create_time: true
      }
    }
  },
  
  // Token管理配置
  token: {
    expireDays: 14,
    expireKeywords: ['过期', '重新', '登录']
  }
};

// 设置zuhaowanAxios的baseURL
zuhaowanAxios.defaults.baseURL = appConfig.zuhaowan.baseUrl;

/**
 * Result工具类 - 统一处理成功和失败状态
 */
class Result {
  constructor(success, data, error) {
    this.success = success;
    this.data = data;
    this.error = error;
  }
  
  static ok(data) {
    return new Result(true, data, null);
  }
  
  static fail(error) {
    return new Result(false, null, error);
  }
  
  isSuccess() {
    return this.success;
  }
  
  isFail() {
    return !this.success;
  }
}

/**
 * 安全执行异步函数，捕获异常并返回Result
 * @param {Function} asyncFn 异步函数
 * @param {String} errorContext 错误上下文
 * @returns {Promise<Result>} Result对象
 */
async function safeExecute(asyncFn, errorContext = '操作失败') {
  try {
    const result = await asyncFn();
    return Result.ok(result);
  } catch (error) {
    console.error(`${errorContext}:`, error);
    return Result.fail(error.message || errorContext);
  }
}

/**
 * 统一错误处理函数
 * @param {String} context 错误上下文
 * @returns {Object} 格式化的错误响应
 */
function handleError(context) {
  console.error(context);
  return { code: -1, message: context };
}

/**
 * 统一异常处理装饰器
 * @param {Function} handler 业务处理函数
 * @returns {Function} 包装后的函数
 */
function withErrorHandling(handler) {
  return async (...args) => {
    try {
      const result = await handler(...args);
      return result;
    } catch (error) {
      console.error('未捕获的异常:', error);
      return handleError('处理请求失败');
    }
  };
}

/**
 * 主函数入口
 */
exports.main = withErrorHandling(async (event, context) => {
  const { action, params = {} } = event;
  const uniIdToken = event.uniIdToken;
  
  // 根据action调用对应的处理函数
  switch (action) {
    case 'getToken':
      return await handleGetToken(params, uniIdToken);
    default:
      return { code: -1, message: '未知操作: ' + action };
  }
});

/**
 * 处理获取令牌请求
 * @param {Object} params 请求参数
 * @param {String} uniIdToken 用户token
 */
async function handleGetToken(params, uniIdToken) {
  const { orderCode } = params;
  if (!orderCode) {
    return { code: -1, message: '请输入订单编号或解锁码' };
  }
  
  // 并行处理用户验证和订单查询
  const [userInfoResult, orderResult] = await Promise.all([
    getUserInfo(uniIdToken),
    findOrder(orderCode)
  ]);
  
  // 检查用户信息获取结果
  if (userInfoResult.isFail()) {
    return { code: -1, message: userInfoResult.error };
  }
  
  const userInfo = userInfoResult.data;
  console.log('用户信息获取成功，用户ID:', userInfo._id);
  
  // 验证用户是否已实名认证
  if (!isUserCertified(userInfo)) {
    return { code: -1, message: '请先完成实名认证' };
  }
  
  // 检查订单查询结果
  if (orderResult.isFail()) {
    return { code: -1, message: orderResult.error };
  }
  
  const order = orderResult.data;
  if (!order) {
    return { code: -1, message: '未找到该订单,请确认后重试' };
  }
  
  // 检查订单是否已结束
  if (order.end_time && isOrderExpired(order.end_time)) {
    return { code: -1, message: `当前订单已结束` };
  }
  
  // 检查订单中是否有jsm字段
  if (!order.jsm) {
    return { code: -1, message: '无法获取令牌,订单信息不完整' };
  }
  
  // 获取Steam令牌
  const steamTokenResult = await getSteamToken(order.jsm);
  if (steamTokenResult.isFail()) {
    return { code: -1, message: steamTokenResult.error };
  }
  
  const steamToken = steamTokenResult.data;
  console.log(`用户 ${userInfo._id} 获取令牌成功,订单编号: ${order.order_num}, jsm值: ${order.jsm}`);
  
  // 关联用户和订单 - 不阻塞主流程
  updateOrderUserAssociation(order.order_num, userInfo._id);
  
  return {
    code: 0,
    message: '获取令牌成功',
    steamToken: steamToken
  };
}

/**
 * 检查订单是否已过期
 * @param {String|Date} endTimeStr 结束时间
 * @returns {Boolean} 是否已过期
 */
function isOrderExpired(endTimeStr) {
  const endTime = new Date(endTimeStr);
  // 直接比较当前时间与结束时间，考虑到时区差异
  const now = new Date();
  const beijingNow = new Date(now.getTime() + 8 * 60 * 60 * 1000);
  // const beijingNow = new Date(now.getTime());
  console.log('当前时间(UTC)：', now);
  console.log('订单结束时间：', endTime);
  console.log('当前北京时间(UTC+8)：', beijingNow);
  return beijingNow > endTime;
}

/**
 * 验证用户是否已完成实名认证
 * @param {Object} userInfo 用户信息
 * @returns {Boolean} 是否已认证
 */
function isUserCertified(userInfo) {
  if (!userInfo.realname_auth) return false;
  if (!userInfo.realname_auth.auth_status) return false;
  if (userInfo.realname_auth.auth_status !== 2) return false;
  return true;
}

/**
 * 验证token并获取用户信息
 * @param {String} uniIdToken 用户token
 * @returns {Promise<Result>} 包含用户信息的Result对象
 */
async function getUserInfo(uniIdToken) {
  return await safeExecute(async () => {
    const { result } = await uniCloud.callFunction({
      name: 'user-auth',
      data: {
        action: 'getUserInfo',
        uniIdToken
      }
    });
    
    if (result.code !== 0) {
      throw new Error('未登录或登录已过期');
    }
    
    return result.userInfo;
  }, '获取用户信息失败');
}

/**
 * 获取第三方平台token
 * @returns {Promise<Result>} 包含第三方token的Result对象
 */
async function getThirdPartyToken() {
  return await safeExecute(async () => {
    // 先查询是否有token
    const tokenResult = await findValidToken();
    if (tokenResult.isSuccess() && tokenResult.data) {
      return tokenResult.data.token;
    }
    
    // 如果没有token，获取新token
    const newTokenResult = await createNewToken();
    if (newTokenResult.isFail()) {
      throw new Error(newTokenResult.error);
    }
    
    return newTokenResult.data;
  }, '获取第三方token失败');
}

/**
 * 查找有效的token
 * @returns {Promise<Result>} 包含token记录的Result对象
 */
async function findValidToken() {
  return await safeExecute(async () => {
    const { serviceName } = appConfig.zuhaowan;
    // 只查询必要的字段
    const tokenRecord = await db.collection('third-party-tokens')
      .where({ 
        service: serviceName,
        status: 1 // 只获取有效的token
      })
      .field({ token: true })
      .orderBy('create_time', 'desc')
      .limit(1)
      .get();
    
    return tokenRecord.data && tokenRecord.data.length ? tokenRecord.data[0] : null;
  }, '查找有效token失败');
}

/**
 * 创建新token
 * @returns {Promise<Result>} 包含新token的Result对象
 */
async function createNewToken() {
  return await safeExecute(async () => {
    const { endpoints, loginParams, serviceName } = appConfig.zuhaowan;
    const { expireDays } = appConfig.token;
    console.log('调用第三方登录接口获取新token');
    
    // 调用第三方登录接口获取新token
    const loginResponse = await zuhaowanAxios.post(endpoints.login, loginParams);
    console.log('第三方登录接口调用成功，状态码:', loginResponse.data.code);
    
    if (loginResponse.data.code !== 200 || !loginResponse.data.data.token) {
      throw new Error(`第三方登录失败: ${loginResponse.data.message || '未知错误'}`);
    }
    
    const newToken = loginResponse.data.data.token;
    console.log('新token:', newToken);
    const now = Date.now();
    
    // 先将所有旧token标记为无效 - 不使用事务
    await db.collection('third-party-tokens')
      .where({
        service: serviceName,
        status: 1
      })
      .update({
        status: 0,
      });
    
    // 保存新token
    await db.collection('third-party-tokens').add({
      service: serviceName,
      token: newToken,
      create_time: now,
      expire_time: now + expireDays * 24 * 60 * 60 * 1000,
      status: 1
    });
    
    return newToken;
  }, '创建新token失败');
}

/**
 * 获取订单列表
 * @param {String} thirdPartyToken 第三方token
 * @returns {Promise<Result>} 包含订单列表的Result对象
 */
async function getOrderList(thirdPartyToken) {
  console.log('---------------------------调用第三方订单列表');
  return await safeExecute(async () => {
    const { endpoints } = appConfig.zuhaowan;
    const { pageSize } = appConfig.order.query;
    const orderParams = {
      page: 1,
      page_size: pageSize,
      // begin_date: '',
      // end_date: ''
    };
    
    // 调用第三方接口获取订单列表
    const orderResponse = await zuhaowanAxios.post(
      endpoints.orderList,
      orderParams,
      {
        headers: {
          'Token': thirdPartyToken
        }
      }
    );
    
    console.log('---------------------------获取订单列表数量：', orderResponse.data.data.list.length);
    // 检查响应状态
    const { code, message, data } = orderResponse.data;
    
    if (code !== 200) {
      // 判断token是否过期
      if (isTokenExpired(code, message)) {
        throw new Error('TOKEN_EXPIRED');
      }
      
      throw new Error(`获取订单列表失败: ${message || '未知错误'}`);
    }
    
    return data?.list || [];
  }, '获取订单列表失败');
}

/**
 * 判断token是否过期
 * @param {Number} code 响应状态码
 * @param {String} message 响应消息
 * @returns {Boolean} 是否过期
 */
function isTokenExpired(code, message) {
  const { expireKeywords } = appConfig.token;
  
  return code === 401 || (message && expireKeywords.some(keyword => message.includes(keyword)));
}

/**
 * 格式化订单对象为统一格式
 * @param {Object} rawOrder 原始订单数据
 * @returns {Object} 格式化后的订单对象
 */
function formatOrderObject(rawOrder) {
  return {
    _id: rawOrder.order_num,
    order_num: rawOrder.order_num,
    jsm: rawOrder.jsm,
    order_status: rawOrder.order_status,
    end_time: rawOrder.etimer,
    unlock_code: rawOrder.youxi
  };
}

/**
 * 在订单列表中查找匹配的订单
 * @param {Array} orderList 订单列表
 * @param {String} identifier 订单编号或解锁码
 * @returns {Object|null} 找到的订单或null
 */
function findOrderInList(orderList, identifier) {
  if (!orderList || !orderList.length) return null;
  
  // 先尝试按订单编号匹配
  let matchedOrder = orderList.find(order => order.order_num === identifier);
  
  // 如果没找到，尝试按解锁码匹配
  if (!matchedOrder) {
    matchedOrder = orderList.find(order => order.youxi === identifier);
  }
  
  return matchedOrder;
}

/**
 * 判断订单是否需要刷新（针对续租场景）
 * @param {Object} order 订单对象
 * @returns {Boolean} 是否需要刷新
 */
function shouldRefreshOrder(order) {
  if (!order || !order.end_time) return false;
  
  const { refresh } = appConfig.order;
  const now = Date.now();
  const endTime = new Date(order.end_time).getTime();
  const lastUpdate = order.update_time || order.create_time || 0;
  
  // 如果订单已经明确过期，需要刷新确认
  if (endTime <= now) {
    return true;
  }
  
  // 如果距离过期时间在阈值内，且距离上次更新超过最小间隔，则需要刷新
  const timeToExpire = endTime - now;
  const timeSinceLastUpdate = now - lastUpdate;
  
  return timeToExpire <= refresh.threshold && timeSinceLastUpdate >= refresh.minInterval;
}

/**
 * 刷新单个订单信息
 * @param {String} orderIdentifier 订单编号或解锁码
 * @param {Object} localOrder 本地订单数据
 * @returns {Promise<Result>} 包含最新订单信息的Result对象
 */
async function refreshSingleOrder(orderIdentifier, localOrder) {
  return await safeExecute(async () => {
    console.log(`刷新订单信息: ${orderIdentifier}`);
    
    // 获取第三方token
    const thirdPartyTokenResult = await getThirdPartyToken();
    if (thirdPartyTokenResult.isFail()) {
      throw new Error(thirdPartyTokenResult.error);
    }
    
    const thirdPartyToken = thirdPartyTokenResult.data;
    
    // 获取订单列表
    const orderListResult = await getOrderList(thirdPartyToken);
    if (orderListResult.isFail()) {
      // 如果是token过期错误，尝试处理
      if (orderListResult.error.includes('TOKEN_EXPIRED')) {
        const retryResult = await handleTokenExpired(orderIdentifier);
        if (retryResult.isSuccess() && retryResult.data) {
          return retryResult.data;
        }
      }
      throw new Error(orderListResult.error);
    }
    
    const orderList = orderListResult.data;
    
    // 使用通用函数查找匹配的订单
    const matchedOrder = findOrderInList(orderList, orderIdentifier);
    
    if (matchedOrder) {
      console.log(`找到最新订单信息，结束时间: ${matchedOrder.etimer}`);
      
      // 异步保存所有订单到数据库（包括当前订单的更新）
      saveOrdersToDatabase(orderList);
      
      // 使用通用函数格式化订单对象
      return formatOrderObject(matchedOrder);
    }
    
    // 如果第三方也找不到，返回本地订单
    console.log(`第三方未找到订单 ${orderIdentifier}，使用本地数据`);
    return localOrder;
  }, `刷新订单${orderIdentifier}失败`);
}

/**
 * 保存订单数据到数据库
 * @param {Array} orders 订单列表
 * @returns {Promise<void>} 无返回值，错误会被记录但不影响主流程
 */
async function saveOrdersToDatabase(orders) {
  if (!orders || !orders.length) return;
  
  const result = await safeExecute(async () => {
    const orderNums = orders.map(order => order.order_num);
    
    // 获取已存在的订单，只查询必要字段
    const existingOrders = await db.collection('uni-id-base-order')
      .where({
        order_num: db.command.in(orderNums)
      })
      .field({ order_num: true, order_status: true, end_time: true })
      .get();
    
    // 创建映射以便快速查找
    const existingOrdersMap = existingOrders.data.reduce((acc, order) => {
      acc[order.order_num] = order;
      return acc;
    }, {});
    
    // 分类订单：新订单和需要更新的订单
    const newOrders = [];
    const ordersToUpdate = [];
    
    orders.forEach(order => {
      const existingOrder = existingOrdersMap[order.order_num];
      
      if (!existingOrder) {
        // 新订单
        newOrders.push({
          _id: order.order_num,
          order_num: order.order_num,
          title: order.pn,
          type: 'zuhaowan',
          info: JSON.stringify(order),
          create_time: order.stimer,
          end_time: order.etimer,
          total_fee: order.pm,
          hour_fee: order.pmoney,
          real_fee: order.real_amount,
          sys_fee: order.all_sys_fee,
          jsm: order.jsm,
          order_status: order.order_status,
          is_relet_order: order.is_relet_order,
          is_refund: order.order_status == '已撤单' ? 1 : 0,
          lease_term: order.zq,
          unlock_code: order.youxi
        });
      } else if ((existingOrder.order_status !== order.order_status) || (existingOrder.end_time !== order.etimer)) {
        console.log('更新订单：', existingOrder.end_time, order.etimer, existingOrder.order_status, order.order_status);
        // 需要更新的订单
        ordersToUpdate.push({
          id: order.order_num,
          data: {
            order_status: order.order_status,
            is_refund: order.order_status == '已撤单' ? 1 : 0,
            real_fee: order.real_amount,
            sys_fee: order.all_sys_fee,
            update_time: Date.now(),
            end_time: order.etimer
          }
        });
      }
    });
    
    // 并行处理新增和更新操作
    const operations = [];
    
    if (newOrders.length > 0) {
      console.log(newOrders.length, '个订单新增完成');
      operations.push(
        db.collection('uni-id-base-order').add(newOrders)
      );
    }
    
    if (ordersToUpdate.length > 0) {
      console.log(`需要更新${ordersToUpdate.length}个订单状态`);
      // 逐个更新订单，不使用事务
      for (const order of ordersToUpdate) {
        operations.push(
          db.collection('uni-id-base-order')
            .doc(order.id)
            .update(order.data)
        );
      }
    }
    
    if (operations.length > 0) {
      await Promise.all(operations);
    }
    console.log(ordersToUpdate.length, '个订单状态更新完成');
  }, '保存订单数据失败');
  
  // 如果失败，只记录错误，不影响主流程
  if (result.isFail()) {
    console.error('保存订单数据失败:', result.error);
  }
}

/**
 * 查找订单
 * @param {String} orderIdentifier 订单编号或解锁码
 * @returns {Promise<Result>} 包含订单信息的Result对象
 */
async function findOrder(orderIdentifier) {
  return await safeExecute(async () => {
    // 使用配置中的查询字段
    const requiredFields = appConfig.order.query.requiredFields;
    
    // 先从数据库查询 - 同时查询order_num和unlock_code
    const dbOrder = await db.collection('uni-id-base-order')
      .where(db.command.or([
        { order_num: orderIdentifier },
        { unlock_code: orderIdentifier }
      ]))
      .field(requiredFields)
      .limit(1)
      .get();
    
    if (dbOrder.data && dbOrder.data.length > 0) {
      const localOrder = dbOrder.data[0];
      console.log('从数据库找到订单');
      
      // 检查是否需要刷新订单信息（针对续租场景）
      if (shouldRefreshOrder(localOrder)) {
        console.log('订单可能已续租，需要刷新最新信息');
        const refreshResult = await refreshSingleOrder(orderIdentifier, localOrder);
        
        if (refreshResult.isSuccess()) {
          return refreshResult.data;
        } else {
          console.log('刷新失败，使用本地订单数据:', refreshResult.error);
          return localOrder;
        }
      }
      
      return localOrder;
    }
    
    // 数据库中没有,从第三方获取最新订单列表
    console.log('数据库中未找到订单,尝试从第三方获取');
    const thirdPartyTokenResult = await getThirdPartyToken();
    
    if (thirdPartyTokenResult.isFail()) {
      throw new Error(thirdPartyTokenResult.error);
    }
    
    const thirdPartyToken = thirdPartyTokenResult.data;
    
    // 获取订单列表
    const orderListResult = await getOrderList(thirdPartyToken);
    
    if (orderListResult.isFail()) {
      // 如果是token过期错误，尝试处理
      if (orderListResult.error.includes('TOKEN_EXPIRED')) {
        const retryResult = await handleTokenExpired(orderIdentifier);
        if (retryResult.isFail()) {
          throw new Error(retryResult.error);
        }
        return retryResult.data;
      } else {
        throw new Error(orderListResult.error);
      }
    }
    
    const orderList = orderListResult.data;
    
    // 使用通用函数查找匹配的订单
    const matchedOrder = findOrderInList(orderList, orderIdentifier);
    
    if (matchedOrder) {
      console.log('在第三方订单列表中找到订单');
      
      // 异步保存订单到数据库，不阻塞主流程
      saveOrdersToDatabase(orderList);
      
      // 使用通用函数格式化订单对象
      return formatOrderObject(matchedOrder);
    }
    
    // 如果都没找到,返回null
    return null;
  }, '查找订单失败');
}

/**
 * 处理token过期情况
 * @param {String} orderIdentifier 订单编号或解锁码
 * @returns {Promise<Result>} 包含订单信息的Result对象
 */
async function handleTokenExpired(orderIdentifier) {
  return await safeExecute(async () => {
    console.log('Token过期,重新获取');
    
    // 直接获取新token
    const newTokenResult = await createNewToken();
    if (newTokenResult.isFail()) {
      throw new Error(newTokenResult.error);
    }
    
    const newToken = newTokenResult.data;
    
    // 使用新token获取订单列表
    const orderListResult = await getOrderList(newToken);
    if (orderListResult.isFail()) {
      throw new Error(orderListResult.error);
    }
    
    const orderList = orderListResult.data;
    
    // 使用通用函数查找匹配的订单
    const matchedOrder = findOrderInList(orderList, orderIdentifier);
    
    // 异步保存订单到数据库，不阻塞主流程
    saveOrdersToDatabase(orderList);
    
    if (matchedOrder) {
      console.log('在第三方订单列表中找到订单');
      // 使用通用函数格式化订单对象
      return formatOrderObject(matchedOrder);
    }
    
    return null;
  }, '处理token过期失败');
}

/**
 * 获取Steam令牌
 * @param {String} jsm 角色标识
 * @returns {Promise<Result>} 包含Steam令牌的Result对象
 */
async function getSteamToken(jsm) {
  return await safeExecute(async () => {
    // 拼接URL
    const { apiUrl, password } = appConfig.steam;
    const url = `${apiUrl}/${jsm}/TwoFactorAuthentication/Token?password=${password}`;
    console.log('请求Steam令牌URL:', url);
    
    // 记录返回结果用于调试
    const response = await steamAxios.get(url);
    console.log('Steam令牌API调用成功，状态:', response.data.Success);
    
    // 检查响应是否成功
    if (!response.data || !response.data.Success) {
      throw new Error(`获取Steam令牌失败: ${response.data ? response.data.Message : '服务器无响应'}`);
    }
    
    //格式1: { "Result": { "UE0002": { "Result": "D9NJ9", "Message": "成功！", "Success": true } }, "Message": "OK", "Success": true }
    // 处理响应格式
    if (response.data.Result && response.data.Result[jsm]) {
      const result = response.data.Result[jsm];
      if (!result.Success) {
        throw new Error(`获取Steam令牌失败: ${result.Message || '获取失败'}`);
      }
      return result.Result;
    }
    
    // 格式2: 可能的其他响应格式，需根据实际情况调整
    if (response.data.token) {
      return response.data.token;
    }
    
    // 如果无法解析，则返回错误
    throw new Error('无法解析Steam令牌响应格式');
  }, '获取Steam令牌失败');
}

/**
 * 更新订单关联的用户ID
 * @param {String} orderNum 订单编号
 * @param {String} userId 用户ID
 * @returns {Promise<void>} 无返回值，错误会被记录但不影响主流程
 */
async function updateOrderUserAssociation(orderNum, userId) {
  const result = await safeExecute(async () => {
    // 使用数组操作符直接添加用户ID，避免重复
    await db.collection('uni-id-base-order')
      .where({ order_num: orderNum })
      .update({
        user_ids: db.command.addToSet(userId),
        update_time: Date.now()
      });
  }, '更新订单用户关联失败');
  
  // 如果失败，只记录错误，不影响主流程
  if (result.isFail()) {
    console.error('更新订单用户关联失败:', result.error);
  }
} 