<template>
    <view class="container">
        <view class="header">
            <u-status-bar bgColor="transparent"></u-status-bar>
            <view class="header-content">
                <view class="header-text">
                    <text class="app-name">优易电玩</text>
                    <view class="app-desc">专业服务 · 品质游戏</view>
                </view>
            </view>
        </view>

        <view class="main-content">
            <view class="section-title">
                <text class="section-title-text">获取令牌</text>
                <!-- <view class="earn-money-link" @click="navigateToCoRenting">闲置账号赚钱</view> -->
            </view>
            <view class="input-group">
                <u-input
                    v-model="orderCode"
                    type="text"
                    placeholder="请输入订单号/解锁码"
                    border="none"
                    fontSize="30rpx"
                    color="#ffffff"
                    inputAlign="left"
                    placeholderStyle="color: rgba(255, 255, 255, 0.3)"
                ></u-input>
            </view>
            <button
                v-if="isLogin"
                class="default-btn"
                hover-class="default-btn-hover"
                type="primary"
                :loading="isLoading"
                :disabled="cooldownTime > 0"
                @click="getToken"
            >{{ cooldownTime > 0 ? `${cooldownTime}秒后重新获取` : '获取令牌' }}</button>
            
            <button
                v-else
                class="default-btn"
                hover-class="default-btn-hover"
                type="primary"
                :loading="isLoading"
                open-type="getPhoneNumber" 
                @getphonenumber="authLogin"
            >授权登录</button>

            <!-- #ifdef MP-WEIXIN -->
            <button 
                v-if="!isLogin"
                open-type="getPhoneNumber" 
                @getphonenumber="getWxPhoneNumber"
                class="wx-auth-btn"
            >微信一键登录</button>
            <!-- #endif -->

            <view class="warning">
                <view class="warning-title">
                    <image class="title-icon" src="/static/images/warn.svg" mode="aspectFit"></image>
                    <text>切勿以身试法</text>
                </view>
                <view class="warning-list">
                    <text class="tip"></text>
                    <view class="warning-item">
                        严禁使用
                        <text class="tip">鼠标宏</text>、
                        <text class="tip">DMA</text>、
                        <text class="tip">芯片</text>、
                        外挂程序等任何作弊手段，严禁恶意
                        <text class="tip">毁坏账号</text>、
                        <text class="tip">分解饰品</text>、
                        <text class="tip">滥用游戏Bug</text>、
                        <text class="tip">击杀队友</text>。
                        违者必追究法律责任。
                    </view>
                    <view class="warning-item">网吧用户请重启电脑后再上号，避免外挂残留。</view>
                    <view class="warning-item">仅限查询本人订单，非法查询将承担全部损失责任！</view>
                    <!-- <view class="warning-item">严禁转租</view> -->
                </view>
            </view>

            <view class="guide">
                <view class="guide-title">
                    <image class="title-icon" src="/static/images/help.svg" mode="aspectFit"></image>
                    <text>常见问题</text>
                </view>
                <view class="guide-steps">
                    <view 
                        class="guide-step" 
                        :class="item.isLink ? 'guide-step-link' : ''" 
                        v-for="item in helpInfoList" 
                        :key="item.text" 
                        @click="showHelp(item)"
                    >{{ item.text }}</view>
                </view>
            </view>
            
            <!-- 使用组件 -->
            <token-popup
            :show="showTokenModal"
            :token="token"
            :is-refreshing="isRefreshing"
            :cooldownTime="cooldownTime"
            @update:show="val => showTokenModal = val"
            @refresh="refreshToken"
            @close="closeTokenPopup"
            ></token-popup>
            
            <help-popup
            :show="showHelpModal"
            @update:show="val => showHelpModal = val"
            :helpInfo="helpInfoItem"
            ></help-popup>
        </view>
        <u-safe-bottom></u-safe-bottom>
    </view>
</template>

<script>
import TokenPopup from '@/components/TokenPopup.vue'
import HelpPopup from '@/components/HelpPopup.vue'
import { mapGetters } from 'vuex'

export default {
    name: 'HomePage',
    components: {
        TokenPopup,
        HelpPopup,
    },
    data() {
        return {
            orderCode: '',
            token: '',
            isLoading: false,
            isRefreshing: false, // 刷新令牌
            showTokenModal: false, // 令牌
            showHelpModal: false, // 帮助
            cooldownTime: 0, // 倒计时秒数
            cooldownTimer: null, // 倒计时定时器
            helpInfoList: [
                {
                    isLink: true,
                    text: '订单编号是什么？如何查看订单编号？',
                    title: '订单编号是什么？',
                    help: [
                        { type: 'text', content: '订单编号是（租号玩）下单后的订单号：' },
                        { type: 'image', content: 'https://mp-6bdae482-9b9c-4ff3-a97f-34cd00c6a5e4.cdn.bspapp.com/base/image/dingdanhao1.png' },
                        { type: 'text', content: '如何复制订单编号：' },
                        { type: 'image', content: 'https://mp-6bdae482-9b9c-4ff3-a97f-34cd00c6a5e4.cdn.bspapp.com/base/image/dingdanhao2.png' },
                        { type: 'image', content: 'https://mp-6bdae482-9b9c-4ff3-a97f-34cd00c6a5e4.cdn.bspapp.com/base/image/dingdanhao3.png' },
                    ]
                },
                {
                    isLink: true,
                    text: '找不到游戏怎么办？游戏被隐藏、私密怎么办？',
                    title: '找不到游戏怎么办？',
                    help: [
                        { type: 'text', content: '取消隐藏：' },
                        { type: 'image', content: 'https://mp-6bdae482-9b9c-4ff3-a97f-34cd00c6a5e4.cdn.bspapp.com/base/image/yincang1.png' },
                        { type: 'image', content: 'https://mp-6bdae482-9b9c-4ff3-a97f-34cd00c6a5e4.cdn.bspapp.com/base/image/yincang1.png' },
                        { type: 'text', content: '取消私密：' },
                        { type: 'image', content: 'https://mp-6bdae482-9b9c-4ff3-a97f-34cd00c6a5e4.cdn.bspapp.com/base/image/simi1.png' },
                    ]
                }
            ],
            helpInfoItem: {}
        }
    },

    computed: {
        // 使用mapGetters从vuex中映射计算属性
        ...mapGetters([
            'isLoggedIn',
            'isCertified',
            'getCertificationInfo'
        ]),
        
        // 登录状态语义化别名
        isLogin() {
            return this.isLoggedIn
        }
    },

    async created() {
        // 初始化应用
        await this.$store.dispatch('initApp')
    },

    methods: {
        /**
         * 授权登录方法
         * @param {Object} e - 事件对象
         */
        async authLogin(e) {
            if (!e.detail || e.detail.errMsg !== 'getPhoneNumber:ok') {
                this.showToast('授权失败')
                return
            }
            
            this.isLoading = true
            
            try {
                // 根据平台调用不同登录方法
                // #ifdef MP-ALIPAY
                await this.handleAlipayLogin(e)
                // #endif
                
                // #ifdef H5
                this.showToast('H5环境暂不支持授权登录', 2000)
                // #endif
                
                // #ifdef MP-WEIXIN
                this.showToast('请点击下方按钮获取手机号', 2000)
                // #endif
            } catch (error) {
                console.error('授权登录异常:', error)
                this.showToast('授权登录失败')
            } finally {
                this.isLoading = false
            }
        },
        
        /**
         * 处理支付宝登录逻辑
         */
        handleAlipayLogin() {
            return new Promise((resolve, reject) => {
                my.getPhoneNumber({
                    success: async (res) => {
                        try {
                            if (!res.response) {
                                this.showToast('获取手机号失败')
                                return resolve()
                            }
                            
                            // 调用云函数解密手机号并登录
                            const result = await this.$cloud.userAuth.loginByMobile({
                                encryptedData: res.response,
                                autoRegister: true
                            })
                            
                            if (result.code === 0) {
                                this.showToast('登录成功', 'success')
                            }
                            resolve()
                        } catch (error) {
                            reject(error)
                        }
                    },
                    fail: (err) => {
                        console.error('获取手机号失败:', err)
                        this.showToast('获取手机号失败')
                        resolve()
                    }
                })
            })
        },
        
        /**
         * 微信小程序获取手机号回调
         * @param {Object} e - 事件对象
         */
        async getWxPhoneNumber(e) {
            if (e.detail.errMsg !== 'getPhoneNumber:ok') {
                this.showToast('您已取消授权')
                return
            }
            
            this.isLoading = true
            try {
                // 调用云函数解密手机号并登录
                const result = await this.$cloud.userAuth.loginByWxMobile({
                    encryptedData: e.detail.encryptedData,
                    iv: e.detail.iv,
                    autoRegister: true
                })
                
                if (result.code === 0) {
                    this.showToast('登录成功', 'success')
                }
            } catch (error) {
                console.error('微信手机号登录失败:', error)
                this.showToast('登录失败')
            } finally {
                this.isLoading = false
            }
        },
        
        /**
         * 获取令牌
         */
        async getToken() {
            // 检查是否在冷却时间内
            if (this.cooldownTime > 0) {
                this.showToast(`请等待${this.cooldownTime}秒后再试`)
                return
            }

            // 表单验证
            if (!this.validateOrderCode()) {
                return
            }

            // 检查登录和认证状态
            if (!await this.checkAuthStatus()) {
                return
            }

            // 已验证通过，执行获取令牌流程
            this.isLoading = true
            try {
                // 去除首尾空格
                const trimmedOrderCode = this.orderCode.trim()
                
                const result = await this.$cloud.steamTokenApi.getToken({
                    orderCode: trimmedOrderCode
                })
                
                if (result.code === 0) {
                    this.token = result.steamToken
                    this.showTokenModal = true
                    // 启动倒计时
                    this.startCooldown()
                }
            } catch (error) {
                console.error('获取令牌失败:', error)
            } finally {
                this.isLoading = false
            }
        },
        
        /**
         * 验证订单编号
         * @returns {Boolean} - 验证结果
         */
        validateOrderCode() {
            // 去除首尾空格
            const trimmedOrderCode = this.orderCode.trim()
            if (!trimmedOrderCode) {
                this.showToast('请输入订单编号')
                return false
            }
            
            // 验证订单编号格式：只允许数字和字母组合或纯数字
            const orderCodeRegex = /^[a-zA-Z0-9]+$/
            if (!orderCodeRegex.test(trimmedOrderCode)) {
                this.showToast('请输入正确的订单编号')
                return false
            }
            
            return true
        },
        
        /**
         * 检查认证状态并引导用户
         * @returns {Boolean} - 检查结果
         */
        async checkAuthStatus() {
            // 检查登录状态
            if (!this.isLogin) {
                uni.showModal({
                    title: '提示',
                    content: '请先登录后再获取令牌',
                    confirmText: '去登录',
                    success: (res) => {
                        if (res.confirm) {
                            this.showToast('请点击授权登录按钮')
                        }
                    }
                })
                return false
            }
            
            // 检查实名认证状态
            if (!this.isCertified) {
                // uni.showModal({
                //     title: '实名认证',
                //     content: '根据相关法规要求，首次获取令牌需要完成实名认证，是否前往认证？',
                //     success: (res) => {
                //         if (res.confirm) {
                //             this.navigateToVerification()
                //         }
                //     }
                // })
                this.navigateToVerification()
                return false
            }
            
            return true
        },

        /**
         * 刷新令牌
         */
        async refreshToken() {
            // 检查是否在冷却时间内
            if (this.cooldownTime > 0) {
                this.showToast(`请等待${this.cooldownTime}秒后再试`)
                return
            }

            this.isRefreshing = true
            try {
                // 去除首尾空格
                const trimmedOrderCode = this.orderCode.trim()
                
                const result = await this.$cloud.steamTokenApi.getToken({
                    orderCode: trimmedOrderCode
                })
                
                if (result.code === 0) {
                    this.token = result.steamToken
                    // 启动倒计时
                    this.startCooldown()
                }
            } catch (error) {
                console.error('刷新令牌失败:', error)
            } finally {
                this.isRefreshing = false
            }
        },

        /**
         * 跳转到实名认证页面
         */
        navigateToVerification() {
            const currentPage = getCurrentPages()[getCurrentPages().length - 1].route
            const returnUrl = encodeURIComponent(`/${currentPage}`)
            
            uni.navigateTo({
                url: `/pages/identity-verification?returnUrl=${returnUrl}`
            })
        },

        /**
         * 显示帮助弹窗
         */
        showHelp(item) {
            if (!item.isLink) return 
            this.helpInfoItem = item
            this.showHelpModal = true
        },

        /**
         * 关闭令牌弹窗
         */
        closeTokenPopup() {
            this.showTokenModal = false
        },

        /**
         * 显示提示信息
         * @param {String} message - 提示内容
         * @param {String} icon - 图标类型 success/none
         * @param {Number} duration - 显示时间
         */
        showToast(message, icon = 'none', duration = 1500) {
            uni.showToast({
                title: message,
                icon: icon,
                duration: duration
            })
        },

        /**
         * 启动冷却倒计时
         */
        startCooldown() {
            // 清除已有的定时器
            if (this.cooldownTimer) {
                clearInterval(this.cooldownTimer)
            }
            
            // 设置倒计时初始值为10秒
            this.cooldownTime = 10
            
            // 创建定时器，每秒减少倒计时时间
            this.cooldownTimer = setInterval(() => {
                this.cooldownTime--
                
                // 当倒计时结束时清除定时器
                if (this.cooldownTime <= 0) {
                    clearInterval(this.cooldownTimer)
                    this.cooldownTimer = null
                }
            }, 1000)
        },

        /**
         * 跳转到闲置账号赚钱页面
         */
        navigateToCoRenting() {
            uni.navigateTo({
                url: '/pages/co-renting'
            })
        },
    },

    beforeDestroy() {
        // 清除倒计时定时器，防止内存泄漏
        if (this.cooldownTimer) {
            clearInterval(this.cooldownTimer)
            this.cooldownTimer = null
        }
    }
}
</script>

<style lang="scss">
/* 变量定义 */
page {
    --primary-color: #1b2838;
    --primary-dark: #171a21;
    --accent-color: #66c0f4;
    --accent-hover: #1999ff;
    --background: #171a21;
    --text-primary: #ffffff;
    --text-secondary: #8f98a0;
    color: var(--text-primary);
    background-color: var(--background);
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--background);
    background-image: linear-gradient(to bottom,
            rgba(27, 40, 56, 0.8),
            rgba(23, 26, 33, 1));
}

.header {
    background: linear-gradient(180deg, #2a475e 0%, var(--primary-color) 100%);
    border-bottom: 1px solid rgba(102, 192, 244, 0.2);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 100%;
    flex-shrink: 0;
}

.header-content {
    height: 80rpx;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 32rpx;
    box-sizing: border-box;
    
    /* #ifdef MP-ALIPAY */
    padding-left: 90rpx;
    /* #endif */
}

.header-text {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.app-name {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
    letter-spacing: 1rpx;
    line-height: 1;
}

.app-desc {
    font-size: 22rpx;
    color: var(--accent-color);
    font-weight: 500;
    letter-spacing: 1rpx;
    opacity: 0.9;
    line-height: 1;
    position: relative;
    padding-left: 16rpx;
    
    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 2rpx;
        height: 16rpx;
        background: var(--accent-color);
        opacity: 0.6;
    }
}

.main-content {
    flex: 1;
    padding: 60rpx 40rpx 40rpx 40rpx;
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

.section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 50rpx;
    opacity: 0.9;
    .section-title-text {
        display: flex;
        align-items: center;
        height: 36rpx;
        line-height: 36rpx;
        font-size: 34rpx;
        color: var(--text-primary);
        letter-spacing: 1rpx;
        &:before {
            content: "";
            width: 8rpx;
            height: 36rpx;
            background: var(--accent-color);
            border-radius: 4rpx;
            margin-right: 20rpx;
        }
    }
}

.earn-money-link {
    font-size: 28rpx;
    color: #07c160;
    font-weight: 600;
    position: relative;
    padding: 6rpx 16rpx;
    border-radius: 20rpx;
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.1), rgba(7, 193, 96, 0.05));
    border: 1px solid rgba(7, 193, 96, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    animation: pulse 2s infinite;
    
    &::after {
        content: '';
        position: absolute;
        top: -6rpx;
        right: -6rpx;
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: #07c160;
        box-shadow: 0 0 10rpx #07c160;
    }
    
    &:active {
        opacity: 0.8;
        transform: scale(0.98);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(7, 193, 96, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10rpx rgba(7, 193, 96, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(7, 193, 96, 0);
    }
}

.input-group {
    margin-top: 20rpx;
    margin-bottom: 50rpx;
    position: relative;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(102, 192, 244, 0.2);
    border-radius: 8rpx;
    &::before,
    &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
                transparent 0%,
                var(--accent-color) 50%,
                transparent 100%);
        opacity: 0.3;
    }

    &::before {
        top: 0;
    }

    &::after {
        bottom: 0;
        opacity: 0.2;
    }

    /* UniApp 输入框样式覆盖 */
    .u-input {
        height: 90rpx !important;
        padding: 0px 28rpx !important;
        .u-input__content__field-wrapper__field  {
            background: transparent !important;
        }
    }
}

.warning {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.08), rgba(165, 40, 52, 0.08));
    border-radius: 8rpx;
    padding: 44rpx;
    border: 1px solid rgba(220, 53, 69, 0.15);
    margin: 60rpx 0 40rpx;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(to bottom, #dc3545, #a52834);
        opacity: 0.8;
    }
}

.warning-title {
    color: #dc3545;
    font-size: 28rpx;
    font-weight: 600;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    }

.title-icon {
    width: 32rpx;
    height: 32rpx;
    flex-shrink: 0;
}

.warning-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.warning-item {
    color: rgba(255, 255, 255, 0.85);
    font-size: 26rpx;
    line-height: 1.6;
    margin-bottom: 16rpx;
    padding-left: 40rpx;
    position: relative;
    .tip {
        color: #dc3545;
    }
    &::before {
        content: "•";
        position: absolute;
        left: 16rpx;
        color: #dc3545;
        opacity: 0.8;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.guide {
    background: linear-gradient(135deg, rgba(102, 192, 244, 0.08), rgba(27, 40, 56, 0.08));
    border-radius: 8rpx;
    padding: 44rpx;
    border: 1px solid rgba(102, 192, 244, 0.15);
    margin: 40rpx 0 0 0;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(to bottom, var(--accent-color), #2a475e);
        opacity: 0.8;
    }
}

.guide-title {
    color: var(--accent-color);
    font-size: 28rpx;
    font-weight: 600;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.guide-steps {
    list-style: none;
    margin: 0;
    padding: 0;
}

.guide-step {
    font-size: 26rpx;
    line-height: 1.6;
    margin-bottom: 24rpx;
    padding-left: 40rpx;
    position: relative;
    display: flex;
    align-items: flex-start;

    &::before {
        content: "•";
        position: absolute;
        left: 16rpx;
        color: var(--accent-color);
        opacity: 0.8;
        font-size: 24rpx;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.guide-step-link {
    color: var(--accent-color);
    text-decoration: underline;
    opacity: 0.9;
    transition: opacity 0.2s ease;
    cursor: pointer;
    &:active {
        opacity: 0.7;
    }
}

/* 微信登录按钮样式 */
.wx-auth-btn {
    margin-top: 20rpx;
    background: #07c160;
    color: #ffffff;
    border: none;
    border-radius: 8rpx;
    height: 90rpx;
    line-height: 90rpx;
    font-size: 30rpx;
    font-weight: 600;
}
.default-btn {
    border-radius: 4px;
    border: none;
    background: linear-gradient(to right, var(--accent-color), var(--accent-hover));
    height: 90rpx;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    
    &:disabled {
        background: linear-gradient(to right, #7a9cb6, #5c8cb0);
        opacity: 0.7;
        color: rgba(255, 255, 255, 0.8);
    }
}
.default-btn-hover {
    background: linear-gradient(to right, #4d91b8, #1476cc);
    transform: translateY(2rpx);
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    opacity: 0.9;
    transition: all 0.2s ease;
}
</style> 