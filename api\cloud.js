import store from '@/store'

/**
 * 云函数调用封装
 * @param {String} name - 云函数名称
 * @param {Object} data - 请求参数
 * @param {Object} options - 配置选项
 * @returns {Promise} - 返回Promise对象
 */
const callFunction = (name, data = {}, options = {}) => {
    // 默认配置
    const defaultOptions = {
        showLoading: true,     // 是否显示加载提示
        loadingTitle: '请求中...',  // 加载提示文字
        errorTitle: '请求失败',   // 错误提示文字
        showError: true,       // 是否显示错误提示
        autoAuth: true,        // 是否自动添加token
        autoRefreshToken: true, // 是否自动刷新token
        checkUserStatus: true  // 是否检查用户状态
    }

    // 合并配置
    const mergedOptions = { ...defaultOptions, ...options }
    const { showLoading, loadingTitle, errorTitle, showError, autoAuth, autoRefreshToken, checkUserStatus } = mergedOptions

    // 检查用户状态
    if (checkUserStatus) {
        const userInfo = store.getters.getUserInfo
        if (userInfo && userInfo._id && userInfo.status !== 0) {
            // 用户状态异常，不允许请求
            let statusMessage = '账号状态异常，无法执行操作'
            
            // 根据状态码显示不同提示
            switch(userInfo.status) {
                case 1:
                    statusMessage = '账号已被禁用，请联系客服'
                    break
                case 2:
                    statusMessage = '账号审核中，请耐心等待'
                    break
                case 3:
                    statusMessage = '账号审核未通过，请联系客服'
                    break
            }
            
            if (showError) {
                uni.showToast({
                    title: statusMessage,
                    icon: 'none'
                })
            }
            
            return Promise.reject({
                code: 'USER_STATUS_ERROR',
                message: statusMessage
            })
        }
    }

    // 自动添加token
    if (autoAuth) {
        const token = uni.getStorageSync('uni_id_token')
        if (token) {
            data.uniIdToken = token
        }
    }

    // 显示加载提示
    if (showLoading) {
        uni.showLoading({
            title: loadingTitle,
            mask: true
        })
    }

    return new Promise((resolve, reject) => {
        uniCloud.callFunction({
            name,
            data
        }).then(res => {
            // 请求成功
            if (showLoading) uni.hideLoading()

            // 处理响应
            const { result } = res

            // token过期处理
            if (result.code == 'TOKEN_EXPIRED' || result.code == 401) {
                // 清除本地token
                uni.removeStorageSync('uni_id_token')
                uni.removeStorageSync('uni_id_token_expired')

                // 清除用户信息
                store.commit('setUserInfo', {})

                // 显示登录提示
                uni.showModal({
                    title: '提示',
                    content: '登录状态已失效，请重新登录',
                    showCancel: false,
                    success: () => {
                        // 跳转到首页
                        const pages = getCurrentPages()
                        const currentPage = pages[pages.length - 1]
                        if (currentPage.route !== 'pages/index') {
                            uni.reLaunch({
                                url: '/pages/index'
                            })
                        }
                    }
                })

                reject(new Error('登录状态已失效'))
                return
            }

            // 自动更新token
            if (autoRefreshToken && result.token) {
                uni.setStorageSync('uni_id_token', result.token)
                if (result.tokenExpired) {
                    uni.setStorageSync('uni_id_token_expired', result.tokenExpired)
                }
            }

            // 处理成功结果
            if (result.code === 0) {
                resolve(result)
            } else {
                // 业务错误
                if (showError) {
                    uni.showToast({
                        title: result.message || errorTitle,
                        icon: 'none'
                    })
                }
                reject(result)
            }
        }).catch(err => {
            // 请求失败
            if (showLoading) uni.hideLoading()

            if (showError) {
                uni.showToast({
                    title: errorTitle,
                    icon: 'none'
                })
            }

            console.error(`云函数 [${name}] 调用失败:`, err)
            reject(err)
        })
    })
}

/**
 * 用户认证相关云函数封装
 */
const userAuth = {
    /**
     * 检查登录状态
     */
    checkToken() {
        return callFunction('user-auth', {
            action: 'checkToken'
        }, {
            showLoading: true,
            showError: false,
            checkUserStatus: false // 不检查用户状态，因为这是检查token的方法
        })
    },

    /**
     * 获取用户信息
     */
    getUserInfo() {
        return callFunction('user-auth', {
            action: 'getUserInfo'
        }, {
            showLoading: false,
            showError: true,
            checkUserStatus: false // 不检查用户状态，因为这是获取用户信息的方法
        })
    },

    /**
     * 手机号登录
     * @param {Object} params 登录参数
     */
    loginByMobile(params) {
        return callFunction('user-auth', {
            action: 'loginByMobile',
            params
        }, {
            checkUserStatus: false // 不检查用户状态，因为这是登录方法
        }).then(result => {
            // 保存用户信息
            if (result.userInfo) {
                store.commit('setUserInfo', result.userInfo)
            }
            if (result.userInfo.realname_auth) {
                let {auth_status} = result.userInfo.realname_auth
                store.commit('setCertificationInfo', {
                    authStatus: auth_status,
                })
            }
            return result
        })
    },

    /**
     * 微信手机号登录
     * @param {Object} params 登录参数
     */
    loginByWxMobile(params) {
        return callFunction('user-auth', {
            action: 'loginByWxMobile',
            params
        }, {
            checkUserStatus: false // 不检查用户状态，因为这是登录方法
        }).then(result => {
            // 保存用户信息
            if (result.userInfo) {
                store.commit('setUserInfo', result.userInfo)
            }
            return result
        })
    },

    /**
     * 退出登录
     */
    logout() {
        // 清除本地token
        uni.removeStorageSync('uni_id_token')
        uni.removeStorageSync('uni_id_token_expired')

        // 清除用户信息
        store.commit('setUserInfo', {})

        return Promise.resolve({ code: 0, message: '退出成功' })
    }
}

/**
 * 支付宝认证相关云函数封装
 */
const alipayAuth = {
    /**
     * 获取认证状态
     */
    getCertifyStatus() {
        return callFunction('alipay-auth', {
            action: 'getCertifyStatus'
        }, {
            checkUserStatus: false // 不检查用户状态，因为这是获取认证状态的方法
        })
    },

    /**
     * 初始化认证
     * @param {Object} params 认证参数
     */
    initializeCertify(params) {
        return callFunction('alipay-auth', {
            action: 'initializeCertify',
            params
        })
    },

    /**
     * 获取认证URL
     * @param {Object} params 认证参数
     */
    getCertifyUrl(params) {
        return callFunction('alipay-auth', {
            action: 'getCertifyUrl',
            params
        })
    },

    /**
     * 查询认证结果
     * @param {Object} params 查询参数
     */
    queryCertifyResult(params) {
        return callFunction('alipay-auth', {
            action: 'queryCertifyResult',
            params
        })
    }
}

/**
 * 令牌相关云函数封装
 */
const steamTokenApi = {
    /**
     * 获取令牌
     * @param {Object} params 获取令牌参数
     */
    getToken(params) {
        return callFunction('token-api', {
            action: 'getToken',
            params
        })
    },
    
    /**
     * 刷新令牌
     * @param {Object} params 获取令牌参数
     */
    refreshToken(params) {
        return callFunction('token-api', {
            action: 'getToken',
            params
        }, {
            showLoading: true,
            loadingTitle: '刷新令牌中...'
        })
    },
    
    /**
     * 根据订单号获取令牌
     * @param {String} orderCode 订单编号
     */
    getTokenByOrderCode(orderCode) {
        return this.getToken({ orderCode })
    }
}

// 导出云函数对象
export default {
    // 基础调用方法
    callFunction,

    // 业务模块
    userAuth,
    alipayAuth,
    steamTokenApi
}
