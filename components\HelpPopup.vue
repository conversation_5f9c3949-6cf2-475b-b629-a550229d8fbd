<template>
    <u-popup 
        :show="show" 
        mode="center" 
        @close="onClose" 
        round="8" 
        :closeable="true"
        :safeAreaInsetBottom="false"
        :overlayStyle="{
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(8px)',
        }"
        :customStyle="{
            width: '90%',
        }"
    >
        <view class="help-content">
            <view class="help-title">{{ helpInfo.title }}</view>
            <view class="help-main">
                <view v-for="(item, index) in helpInfo.help" :key="index" style="margin-bottom: 20rpx;">
                    <view v-if="item.type == 'text'" class="help-text">{{ item.content }}</view>
                    <u-image 
                        v-if="item.type == 'image'" 
                        :src="item.content"
                        mode="widthFix" 
                        width="100%"
                        height="300rpx"
                        :radius="4"
                        :showLoading="true"
                        bgColor="rgba(0, 0, 0, 0.3)"
                        @click="previewImage(item.content)"
                    >
                        <template v-slot:loading>
                            <u-loading-icon mode="semicircle" text="图片加载中..."></u-loading-icon>
                        </template>
                        <view slot="error" style="font-size: 24rpx;">图片加载失败</view>
                    </u-image>
                </view>
            </view>
        </view>
    </u-popup>
</template>

<script>
export default {
    name: 'HelpPopup',
    
    props: {
        show: {
            type: Boolean,
            default: false
        },
        helpInfo: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },

    emits: ['update:show', 'close'],

    methods: {
        onClose() {
            this.$emit('update:show', false)
            this.$emit('close')
        },
        
        /**
         * 预览图片
         * @param {String} url - 图片地址
         */
        previewImage(url) {
            // 获取当前帮助中所有图片URL
            const imageUrls = this.helpInfo.help
                .filter(item => item.type === 'image')
                .map(item => item.content);
                
            if (imageUrls.length === 0) return;
            uni.previewImage({
                current: url, // 当前显示图片的url
                urls: imageUrls, // 图片url数组
                indicator: 'number', // 指示器类型，支持default(默认)/number(带页码)/none(隐藏)
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.help-content {
    background: linear-gradient(135deg, #2a475e, var(--primary-color));
    padding: 50rpx;
    border-radius: 8rpx;
    position: relative;
    border: 1px solid rgba(102, 192, 244, 0.2);
    box-shadow: 0 0 80rpx rgba(0, 0, 0, 0.5);
    .help-title {
        color: var(--accent-color);
        font-size: 32rpx;
        margin-bottom: 40rpx;
        padding-bottom: 24rpx;
        border-bottom: 1px solid rgba(102, 192, 244, 0.2);
        display: flex;
        align-items: center;
        gap: 16rpx;
    }
    .help-main {
        max-height: 40vh;
        overflow: auto;
        .help-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 28rpx;
            line-height: 1.6;
        }
    }
}
</style> 