
// 导入封装好的axios实例
import request from "./request";
import qs from 'qs' 

export function postBase(data = {}, contentType = "json") {
  const url = data.url;
  let params = data.data;
  const config = {
    method: "post",
    url: url,
    headers: {
      "Content-Type": contentType == "json" ? "application/json" : "application/x-www-form-urlencoded;charset=utf8",
    },
    data: contentType == "json" ? params : qs.stringify(params),
  };
  return request(config);
}
export function getBase(data = {}) {
  const url = data.url;
  let params = data.data;
  const config = {
    method: "get",
    url: url,
    headers: {
      "Content-Type": "application/json",
    },
    params: params,
  };
  return request(config);
}