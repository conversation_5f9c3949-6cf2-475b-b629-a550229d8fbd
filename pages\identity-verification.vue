<template>
    <view class="container">
        <view class="header">
            <u-status-bar bgColor="transparent"></u-status-bar>
            <view class="header-content">
                <!-- #ifndef MP-ALIPAY -->
                <view class="back-button" @click="goBack">
                    <image src="/static/images/back.svg" mode="aspectFit"></image>
                </view>
                <!-- #endif -->
                <view class="header-text">
                    <text class="page-title">实名认证</text>
                </view>
            </view>
        </view>

        <view class="main-content">
            <view class="section-title">请填写真实身份信息</view>
            
            <view class="form-container">
                <view class="input-group">
                    <u-input
                        v-model="realName"
                        type="text"
                        placeholder="请输入真实姓名"
                        border="none"
                        fontSize="30rpx"
                        color="#ffffff"
                        inputAlign="left"
                        placeholderStyle="color: rgba(255, 255, 255, 0.3)"
                    ></u-input>
                </view>
                
                <view class="input-group">
                    <u-input
                        v-model="identityCard"
                        type="text"
                        placeholder="请输入身份证号码"
                        border="none"
                        fontSize="30rpx"
                        color="#ffffff"
                        inputAlign="left"
                        placeholderStyle="color: rgba(255, 255, 255, 0.3)"
                    ></u-input>
                </view>
                
                <button
                    class="default-btn"
                    hover-class="default-btn-hover"
                    type="primary"
                    :loading="isLoading"
                    @click="startVerification"
                >开始认证</button>
            </view>

            <view class="notice-box">
                <view class="notice-title">
                    <image class="title-icon" src="/static/images/info.svg" mode="aspectFit"></image>
                    <text>注意事项</text>
                </view>
                <view class="notice-list">
                    <view class="notice-item">请确保所填写的信息真实有效，一旦提交无法撤销</view>
                    <view class="notice-item">使用支付宝人脸识别进行身份认证，请确保是您本人在操作</view>
                    <!-- <view class="notice-item">认证过程需要网络连接，请确保网络通畅</view>
                    <view class="notice-item">认证信息仅用于身份验证，我们将严格保护您的隐私安全</view> -->
                </view>
            </view>
        </view>
        <u-safe-bottom></u-safe-bottom>
    </view>
</template>

<script>
export default {
    data() {
        return {
            realName: '', // 真实姓名
            identityCard: '', // 身份证号码
            isLoading: false, // 加载状态
            returnUrl: '', // 返回地址
            resultTimer: null // 结果查询定时器
        }
    },
    
    onLoad(options) {
        // 获取返回地址
        if (options.returnUrl) {
            this.returnUrl = decodeURIComponent(options.returnUrl);
        }
        
        // 尝试从store获取用户信息预填充
        const userInfo = this.$store.getters.getUserInfo;
        if (userInfo && userInfo.realname) {
            this.realName = userInfo.realname;
        }
    },
    
    methods: {
        // 返回上一页
        goBack() {
            if (this.returnUrl) {
                uni.redirectTo({
                    url: this.returnUrl
                });
            } else {
                uni.navigateBack();
            }
        },
        
        // 验证身份证号有效性
        validateIdCard(idCard) {
            // 简单验证长度和格式
            const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
            return reg.test(idCard);
        },
        
        // 开始实名认证
        async startVerification() {
            // 表单验证
            if (!this.realName.trim()) {
                uni.showToast({
                    title: '请输入真实姓名',
                    icon: 'none'
                });
                return;
            }
            if (!this.identityCard.trim()) {
                uni.showToast({
                    title: '请输入身份证号码',
                    icon: 'none'
                });
                return;
            }
            
            if (!this.validateIdCard(this.identityCard)) {
                uni.showToast({
                    title: '身份证号码格式不正确',
                    icon: 'none'
                });
                return;
            }
            
            // 开始认证流程
            this.isLoading = true;
            try {
                // 调用认证初始化云函数
                const initResult = await this.$cloud.alipayAuth.initializeCertify({
                    realName: this.realName,
                    identityCard: this.identityCard
                });
                
                // 处理初始化结果
                if (initResult.code === 0 && initResult.data.certifyId) {
                    // 获取认证URL
                    const urlResult = await this.$cloud.alipayAuth.getCertifyUrl({
                        certifyId: initResult.data.certifyId
                    });
                    
                    if (urlResult.code === 0 && urlResult.data.certifyUrl) {
                        const certifyUrlString = urlResult.data.certifyUrl;
                        console.log('认证URL:', certifyUrlString);
                        
                        // #ifdef MP-ALIPAY
                        // 支付宝小程序直接调用认证接口
                        my.startAPVerify({
                            url: certifyUrlString,
                            success: (res) => {
                                console.log('认证结果:', res);
                                this.checkVerificationResult(initResult.data.certifyId);
                            },
                            fail: (err) => {
                                console.error('认证失败:', err);
                                uni.showToast({
                                    title: '认证失败，请重试',
                                    icon: 'none'
                                });
                                this.isLoading = false;
                            }
                        });
                        // #endif
                        
                        // #ifdef H5
                        // H5环境下使用window.location跳转
                        window.location.href = certifyUrlString;
                        // #endif
                        
                        // #ifdef MP-WEIXIN
                        // 微信小程序下提示需要使用支付宝APP完成认证
                        uni.showModal({
                            title: '提示',
                            content: '需要跳转到支付宝APP完成认证，请确认',
                            success: (res) => {
                                if (res.confirm) {
                                    // 实际开发中可使用开放能力打开支付宝
                                    // 这里简化处理，仅提示
                                    uni.showToast({
                                        title: '请在支付宝中完成认证后返回',
                                        icon: 'none',
                                        duration: 3000
                                    });
                                    
                                    // 定时查询认证结果
                                    this.startCheckResultTimer(initResult.data.certifyId);
                                }
                            },
                            complete: () => {
                                this.isLoading = false;
                            }
                        });
                        // #endif
                    } else {
                        uni.showToast({
                            title: urlResult.message || '获取认证链接失败',
                            icon: 'none'
                        });
                        this.isLoading = false;
                    }
                } else {
                    uni.showToast({
                        title: initResult.message || '认证初始化失败',
                        icon: 'none'
                    });
                    this.isLoading = false;
                }
            } catch (error) {
                this.isLoading = false;
            }
        },
        
        // 启动结果查询定时器
        startCheckResultTimer(certifyId) {
            this.resultTimer = setInterval(() => {
                this.checkVerificationResult(certifyId);
            }, 10000); // 每10秒查询一次
            
            // 设置5分钟后自动清除定时器
            setTimeout(() => {
                if (this.resultTimer) {
                    clearInterval(this.resultTimer);
                    this.resultTimer = null;
                    uni.showToast({
                        title: '认证超时，请重新开始认证',
                        icon: 'none'
                    });
                }
            }, 5 * 60 * 1000);
        },
        
        // 检查认证结果
        async checkVerificationResult(certifyId) {
            try {
                const result = await this.$cloud.alipayAuth.queryCertifyResult({
                    certifyId
                });
                
                if (result.code === 0) {
                    // 认证已完成
                    if (result.data.passed === true) {
                        // 认证通过
                        if (this.resultTimer) {
                            clearInterval(this.resultTimer);
                            this.resultTimer = null;
                        }
                        
                        // 更新vuex中的认证状态
                        this.$store.commit('setCertificationInfo', {
                            authStatus: 2,
                            isCertified: true,
                            certifyId: certifyId
                        });
                        
                        uni.showToast({
                            title: '认证成功',
                            icon: 'success'
                        });
                        
                        // 返回到来源页面
                        setTimeout(() => {
                            this.goBack();
                        }, 1500);
                    } else if (result.data.passed === false) {
                        // 认证失败
                        if (this.resultTimer) {
                            clearInterval(this.resultTimer);
                            this.resultTimer = null;
                        }
                        
                        // 更新vuex中的认证状态
                        this.$store.commit('setCertificationInfo', {
                            authStatus: 3,
                            isCertified: false,
                            certifyId: certifyId
                        });
                        
                        uni.showModal({
                            title: '认证未通过',
                            content: result.reason || '身份信息验证未通过，请确保是本人操作',
                            showCancel: false
                        });
                        
                        this.isLoading = false;
                    }
                    // 认证仍在进行中，继续等待定时器下次查询
                }
            } catch (error) {
                console.error('查询认证结果异常:', error);
                // 查询异常不终止定时器，继续等待下次查询
            }
        }
    },
    
    // 组件销毁时清除定时器
    beforeDestroy() {
        if (this.resultTimer) {
            clearInterval(this.resultTimer);
            this.resultTimer = null;
        }
    }
}
</script>

<style lang="scss">
/* 变量使用与主页面保持一致 */
page {
    --primary-color: #1b2838;
    --primary-dark: #171a21;
    --accent-color: #66c0f4;
    --accent-hover: #1999ff;
    --background: #171a21;
    --text-primary: #ffffff;
    --text-secondary: #8f98a0;
    color: var(--text-primary);
    background-color: var(--background);
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--background);
    background-image: linear-gradient(to bottom,
            rgba(27, 40, 56, 0.8),
            rgba(23, 26, 33, 1));
}

.header {
    background: linear-gradient(180deg, #2a475e 0%, var(--primary-color) 100%);
    border-bottom: 1px solid rgba(102, 192, 244, 0.2);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 100%;
    flex-shrink: 0;
}

.header-content {
    height: 80rpx;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 32rpx;
    box-sizing: border-box;
}

.back-button {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
        width: 36rpx;
        height: 36rpx;
    }
}

.header-text {
    flex: 1;
    text-align: center;
    // #ifndef MP-ALIPAY
    margin-left: -60rpx;
    // #endif
}

.page-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 1rpx;
}

.main-content {
    flex: 1;
    padding: 60rpx 40rpx 40rpx 40rpx;
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

.section-title {
    font-size: 34rpx;
    color: var(--text-primary);
    margin-bottom: 50rpx;
    display: flex;
    align-items: center;
    gap: 20rpx;
    letter-spacing: 1rpx;
    opacity: 0.9;
    height: 36rpx;
    line-height: 36rpx;

    &:before {
        content: "";
        width: 8rpx;
        height: 36rpx;
        background: var(--accent-color);
        border-radius: 4rpx;
        flex-shrink: 0;
    }
}

.form-container {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8rpx;
    padding: 30rpx;
    border: 1px solid rgba(102, 192, 244, 0.15);
    margin-bottom: 40rpx;
}

.input-group {
    margin-bottom: 30rpx;
    position: relative;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(102, 192, 244, 0.2);
    border-radius: 8rpx;
    
    &::before,
    &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
                transparent 0%,
                var(--accent-color) 50%,
                transparent 100%);
        opacity: 0.3;
    }

    &::before {
        top: 0;
    }

    &::after {
        bottom: 0;
        opacity: 0.2;
    }

    /* UniApp 输入框样式覆盖 */
    .u-input {
        height: 90rpx !important;
        padding: 0px 28rpx !important;
        .u-input__content__field-wrapper__field  {
            background: transparent !important;
        }
    }
}

.notice-box {
    background: linear-gradient(135deg, rgba(102, 192, 244, 0.08), rgba(27, 40, 56, 0.08));
    border-radius: 8rpx;
    padding: 44rpx;
    border: 1px solid rgba(102, 192, 244, 0.15);
    margin-top: 40rpx;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(to bottom, var(--accent-color), #2a475e);
        opacity: 0.8;
    }
}

.notice-title {
    color: var(--accent-color);
    font-size: 28rpx;
    font-weight: 600;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.title-icon {
    width: 32rpx;
    height: 32rpx;
    flex-shrink: 0;
}

.notice-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.notice-item {
    color: rgba(255, 255, 255, 0.85);
    font-size: 26rpx;
    line-height: 1.6;
    margin-bottom: 16rpx;
    padding-left: 40rpx;
    position: relative;
    display: flex;
    align-items: flex-start;

    &::before {
        content: "•";
        position: absolute;
        left: 16rpx;
        color: var(--accent-color);
        opacity: 0.8;
    }

    &:last-child {
        margin-bottom: 0;
    }
}
.default-btn {
    border: none;
    border-radius: 4px;
    background: linear-gradient(to right, var(--accent-color), var(--accent-hover));
    height: 90rpx;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
}
.default-btn-hover {
    background: linear-gradient(to right, #4d91b8, #1476cc);
    transform: translateY(2rpx);
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    opacity: 0.9;
    transition: all 0.2s ease;
}
</style> 