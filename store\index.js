import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

// 用户模块
const userModule = {
	state: {
		// 用户信息
		userInfo: uni.getStorageSync('userInfo') || {},
		// 登录状态
		isLogin: <PERSON><PERSON><PERSON>(uni.getStorageSync('uni_id_token'))
	},
	mutations: {
		// 设置用户信息
		setUserInfo(state, userInfo) {
			state.userInfo = userInfo
			state.isLogin = Boolean(Object.keys(userInfo).length)
			uni.setStorageSync('userInfo', userInfo)
		}
	},
	actions: {
		// 获取用户信息
		async getUserInfo({ commit }) {
			try {
				const cloud = this._vm.$cloud
				const result = await cloud.userAuth.getUserInfo()
				
				if (result && result.code === 0 && result.userInfo) {
					commit('setUserInfo', result.userInfo)
					return result.userInfo
				} else {
					return null
				}
			} catch (error) {
				console.error('获取用户信息失败:', error)
				return null
			}
		},
		// 登出
		logout({ commit }) {
			// 清除本地token
			uni.removeStorageSync('uni_id_token')
			uni.removeStorageSync('uni_id_token_expired')
			
			// 清除用户信息
			commit('setUserInfo', {})
			
			return { code: 0, message: '退出成功' }
		}
	},
	getters: {
		// 获取用户基本信息
		getUserInfo(state) {
			return state.userInfo
		},
		
		// 判断用户是否登录
		isLoggedIn(state) {
			return state.isLogin
		}
	}
}

// 认证模块
const certificationModule = {
	state: {
		// 认证状态
		certificationInfo: {
			isCertified: false,
			certifyId: '',
			authStatus: 0 // 0-未认证，1-认证中，2-认证成功，3-认证失败
		}
	},
	mutations: {
		// 设置认证信息
		setCertificationInfo(state, info) {
			state.certificationInfo = {
				...state.certificationInfo,
				...info
			}
			// 更新是否已认证的状态
			state.certificationInfo.isCertified = info.authStatus === 2
		}
	},
	getters: {
		// 判断用户是否已完成实名认证
		isCertified(state) {
			return state.certificationInfo.isCertified
		},
		
		// 获取认证状态
		getCertificationInfo(state) {
			return state.certificationInfo
		}
	}
}

// 系统模块
const systemModule = {
	state: {
		// 系统信息
		systemInfo: null
	},
	mutations: {
		// 设置系统信息
		setSystemInfo(state, info) {
			state.systemInfo = info
		}
	},
	getters: {
		// 获取系统信息
		getSystemInfo(state) {
			return state.systemInfo
		}
	}
}

// 根Store
const vuexStore = new Vuex.Store({
	// 合并模块状态
	state: {
		...userModule.state,
		...certificationModule.state,
		...systemModule.state
	},
	
	// 合并模块mutations
	mutations: {
		...userModule.mutations,
		...certificationModule.mutations,
		...systemModule.mutations
	},
	
	// 应用级别的actions
	actions: {
		...userModule.actions,
		
		// 初始化应用
		async initApp({ dispatch, commit }) {
			try {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync()
				commit('setSystemInfo', systemInfo)
				
				// 检查登录状态
				await dispatch('checkLoginState')
				
				return true
			} catch (error) {
				console.error('初始化应用失败:', error)
				return false
			}
		},
		
		// 检查登录状态
		async checkLoginState({ commit, dispatch }) {
			try {
				const token = uni.getStorageSync('uni_id_token')
				if (!token) {
					commit('setUserInfo', {})
					return false
				}
				
				// 调用云函数检查token
				const cloud = this._vm.$cloud
				const result = await cloud.userAuth.checkToken()
				
				// 更新状态
				if (result && result.code === 0) {
					// 检查认证状态
					await dispatch('checkCertificationStatus')
					return true
				} else {
					// 登录失效
					commit('setUserInfo', {})
					return false
				}
			} catch (error) {
				console.error('检查登录状态失败:', error)
				commit('setUserInfo', {})
				return false
			}
		},
		
		// 检查认证状态
		async checkCertificationStatus({ commit, state, dispatch }) {
			if (!state.isLogin) return false
			
			try {
				const cloud = this._vm.$cloud
				const result = await cloud.alipayAuth.getCertifyStatus()
				
				if (result && result.code === 0) {
					// 注意: 根据返回结构确定正确的路径
					const authStatus = result.data.authStatus || 0
					const certifyId = result.data.certifyId || ''
					
					// 更新认证状态
					commit('setCertificationInfo', {
						authStatus,
						certifyId
					})
					
					// 如果是认证中状态，查询最新的认证结果
					if (authStatus === 1 && certifyId) {
						await dispatch('queryCertificationResult', { certifyId })
					}
					
					// 返回是否已认证
					return state.certificationInfo.isCertified
				}
				
				return false
			} catch (error) {
				console.error('检查认证状态失败:', error)
				return false
			}
		},
		
		// 查询认证结果
		async queryCertificationResult({ commit }, { certifyId }) {
			if (!certifyId) return
			
			try {
				const cloud = this._vm.$cloud
				const result = await cloud.alipayAuth.queryCertifyResult({ certifyId })
				
				if (result && result.code === 0 && result.data.passed === true) {
					commit('setCertificationInfo', { authStatus: 2, isCertified: true })
					return true
				}
				return false
			} catch (error) {
				console.error('查询认证结果失败:', error)
				return false
			}
		}
	},
	
	// 合并模块getters
	getters: {
		...userModule.getters,
		...certificationModule.getters,
		...systemModule.getters
	}
});

export default vuexStore
