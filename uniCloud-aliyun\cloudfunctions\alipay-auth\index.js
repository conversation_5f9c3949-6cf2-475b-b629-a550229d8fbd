'use strict';
const uniIdCommon = require('uni-id-common');
const db = uniCloud.database();
const usersCollection = db.collection('uni-id-users');
const logCollection = db.collection('uni-id-log');
const { userConfig, alipayConfig } = require('all-config');
const moment = require('moment');
const AlipaySdk = require('alipay-sdk').default;

/************ 常量定义 ************/
// 错误码常量
const ERROR_CODES = {
  PARAM_ERROR: -1,                // 参数错误
  TOKEN_EXPIRED: 401,             // token已过期
  SYSTEM_ERROR: 'SYSTEM_ERROR'    // 系统错误
};

// 认证状态常量
const AUTH_STATUS = {
  NOT_AUTH: 0,      // 未认证
  IN_PROGRESS: 1,   // 认证中
  SUCCESS: 2,       // 认证成功
  FAILED: 3         // 认证失败
};

/************ 工具函数 ************/
// 支付宝SDK实例化 - 全局单例
const alipaySdk = new AlipaySdk({
  appId: alipayConfig.appId,
  privateKey: alipayConfig.privateKey,
  alipayPublicKey: alipayConfig.alipayPublicKey,
  signType: alipayConfig.signType,
  keyType: alipayConfig.keyType,
  gateway: alipayConfig.gateway
});

// 创建标准响应格式
function createResponse(code = 0, message = 'success', data = {}) {
  return { code, message, data };
}

// 创建错误响应
function createErrorResponse(errorCode, customMessage = '') {
  // 错误码统一，只使用错误码数值
  const code = typeof errorCode === 'object' ? errorCode.code : errorCode;
  const message = customMessage || (typeof errorCode === 'object' ? errorCode.message : '操作失败');
  
  console.error(`[错误响应] code: ${code}, message: ${message}`);
  return { code, message };
}

// 处理异常
function handleError(error) {
  console.error('系统异常:', error);
  return {
    code: ERROR_CODES.SYSTEM_ERROR,
    message: error.message || '系统处理异常'
  };
}

// 记录认证日志 - 改为异步操作，不阻塞主流程
async function logCertify(userId, action, content, status = 0, errorMsg = '') {
  try {
    const now = Date.now();
    
    // 构建日志对象
    const logData = {
      user_id: userId,
      type: 'alipay_certify',
      action,
      content: typeof content === 'object' ? JSON.stringify(content) : content,
      status,
      error_msg: errorMsg,
      ip: '',
      create_date: now,
      create_time: moment(now).utcOffset('+08:00').format('YYYY-MM-DD HH:mm:ss')  // 设置为东八区
    };
    
    // 写入日志表 - 不等待结果
    logCollection.add(logData).catch(err => console.error('记录日志失败:', err));
    console.log(`[认证日志] 用户：${userId}，操作：${action}，状态：${status === 0 ? '成功' : '失败'}`);
  } catch (error) {
    console.error('记录日志失败:', error);
  }
}

/************ 业务功能函数 ************/
// 身份认证初始化
async function initializeCertify(userId, realName, identityCard) {
  try {
    // 检查参数
    if (!userId || !realName || !identityCard) {
      return createErrorResponse(ERROR_CODES.PARAM_ERROR, '缺少必要参数');
    }
    
    // 查询用户当前认证信息
    const userInfo = await usersCollection.doc(userId).field({realname_auth: 1}).get();
    const user = userInfo.data && userInfo.data[0];
    const realnameAuth = user?.realname_auth || {};
    
    // 获取当前认证次数，如果不存在则默认为0
    const currentAuthNum = parseInt(realnameAuth.auth_num || '0', 10);
    const newAuthNum = currentAuthNum + 1;
    
    // 生成外部订单号(使用时间戳+userId后6位)
    const timestamp = moment().format('x');
    const userIdSuffix = userId.substring(Math.max(0, userId.length - 6));
    const outOrderNo = `UEGAME${timestamp}${userIdSuffix}`;
    const content = { outOrderNo, realName, identityCard };
    
    // 调用支付宝身份认证初始化接口
    const result = await alipaySdk.exec('alipay.user.certify.open.initialize', {
      bizContent: {
        outer_order_no: outOrderNo,
        biz_code: 'FACE',
        identity_param: {
          identity_type: 'CERT_INFO',
          cert_type: 'IDENTITY_CARD',
          cert_name: realName,
          cert_no: identityCard
        },
        merchant_config: {
          return_url: alipayConfig.oauth_redirect_url,
          face_reserve_strategy: alipayConfig.reserve_strategy
        }
      }
    });
    
    // 记录日志
    logCertify(userId, 'initialize', {content, result}, 0, '初始化成功');
    
    // 解析结果
    if (result.code === '10000' && result.certifyId) {
      // 更新用户认证状态为"认证中"并递增认证次数
      await usersCollection.doc(userId).update({
        realname_auth: {
          auth_status: AUTH_STATUS.IN_PROGRESS, // 认证中
          auth_type: 'alipay',
          auth_date: Date.now(),
          real_name: realName,
          identity: identityCard, // 脱敏存储
          certify_id: result.certifyId,
          auth_num: String(newAuthNum) // 更新认证次数
        }
      });
      return createResponse(0, '认证初始化成功', {
        certifyId: result.certifyId
      });
    } else {
      // 记录失败日志
      logCertify(userId, 'initialize', {content, result}, 1, result.subMsg || '初始化失败');
      
      return createErrorResponse(
        ERROR_CODES.PARAM_ERROR,
        result.subMsg || '认证初始化失败'
      );
    }
  } catch (error) {
    logCertify(userId, 'initialize', {}, 1, error.message || '认证初始化异常');
    return handleError(error);
  }
}

// 获取认证URL
async function getCertifyUrl(userId, certifyId) {
  try {
    // 检查参数
    if (!userId || !certifyId) {
      return createErrorResponse(ERROR_CODES.PARAM_ERROR, '缺少必要参数');
    }
    
    // 使用pageExec方法同步生成URL字符串，不是异步方法
    const certifyUrl = alipaySdk.pageExec('alipay.user.certify.open.certify', {
      method: 'GET',
      bizContent: {
        certify_id: certifyId
      }
    });
    
    // 记录日志 - 异步操作
    logCertify(userId, 'getCertifyUrl', { certifyId, certifyUrl }, 0, '获取URL成功');
    
    // 返回认证URL，客户端通过这个URL跳转到支付宝认证页面
    return createResponse(0, '获取认证URL成功', {
      certifyUrl
    });
  } catch (error) {
    console.error('获取认证URL异常:', error instanceof Error ? error.message : JSON.stringify(error));
    logCertify(userId, 'getCertifyUrl', { certifyId, error: error instanceof Error ? error.message : JSON.stringify(error) }, 1, '系统异常获取URL失败');
    return handleError(error);
  }
}

// 查询认证结果
async function queryCertifyResult(userId, certifyId) {
  try {
    // 检查参数
    if (!userId || !certifyId) {
      return createErrorResponse(ERROR_CODES.PARAM_ERROR, '缺少必要参数');
    }
    
    // 查询用户当前认证信息，获取认证次数
    const userInfo = await usersCollection.doc(userId).field({realname_auth: 1}).get();
    const user = userInfo.data && userInfo.data[0];
    if (!user || !user.realname_auth) {
      return createErrorResponse(ERROR_CODES.PARAM_ERROR, '用户认证信息不存在');
    }
    
    // 获取当前认证次数
    const realnameAuth = user.realname_auth;
    
    // 调用支付宝身份认证查询接口
    const result = await alipaySdk.exec('alipay.user.certify.open.query', {
      bizContent: {
        certify_id: certifyId
      }
    });
    
    // 记录日志
    logCertify(userId, 'query', { certifyId, result }, 0, '查询认证结果');
    
    // 处理查询结果
    if (result.code === '10000') {
      // 认证通过
      if (result.passed === 'T') {
        // 更新用户认证状态为"认证成功"，不修改其他字段
        await usersCollection.doc(userId).update({
          'realname_auth.auth_status': AUTH_STATUS.SUCCESS, // 认证成功
          'realname_auth.auth_date': Date.now(),
          'realname_auth.auth_error': '认证成功'
        });
        
        return createResponse(0, '认证成功', {
          passed: true
        });
      } 
      // 认证失败
      else if (result.passed === 'F') {
        // 更新用户认证状态为"认证失败"，不修改其他字段
        await usersCollection.doc(userId).update({
          'realname_auth.auth_status': AUTH_STATUS.FAILED, // 认证失败
          'realname_auth.auth_date': Date.now(),
          'realname_auth.auth_error': '身份验证未通过'
        });
        
        return createResponse(0, '认证查询成功', {
          passed: false,
          reason: '身份信息验证未通过，请确保是本人操作'
        });
      }
      // 认证进行中
      else {
        return createResponse(0, '认证进行中', {
          passed: null
        });
      }
    } else {
      // 更新用户认证状态为"认证失败"，不修改其他字段
      await usersCollection.doc(userId).update({
        'realname_auth.auth_status': AUTH_STATUS.FAILED, // 认证失败
        'realname_auth.auth_date': Date.now(),
        'realname_auth.auth_error': result.subMsg
      });
      // 记录失败日志
      logCertify(userId, 'query', { certifyId, result }, 1, result.subMsg || '认证查询失败');
      
      return createErrorResponse(
        ERROR_CODES.PARAM_ERROR,
        result.subMsg || '认证查询失败'
      );
    }
  } catch (error) {
    console.error('查询认证结果异常:', error);
    logCertify(userId, 'query', { certifyId, error }, 1, error.message || '系统异常');
    return handleError(error);
  }
}

// 获取用户认证状态
async function getUserCertifyStatus(userId) {
  try {
    // 检查参数
    if (!userId) {
      return createErrorResponse(ERROR_CODES.PARAM_ERROR, '缺少用户ID');
    }
    
    // 查询用户信息
    const userInfo = await usersCollection.doc(userId).field({
      realname_auth: 1  // 只查询认证相关字段，减少数据传输
    }).get();
    
    // 用户不存在
    if (!userInfo.data || userInfo.data.length === 0) {
      return createErrorResponse(ERROR_CODES.PARAM_ERROR, '用户不存在');
    }
    
    const user = userInfo.data[0];
    const realNameAuth = user.realname_auth || {};
    
    return createResponse(0, '获取认证状态成功', {
      authStatus: realNameAuth.auth_status || AUTH_STATUS.NOT_AUTH,
      // authType: realNameAuth.auth_type || '',
      certifyId: realNameAuth.certify_id || '',
      authDate: realNameAuth.auth_date || null,
      authNum: realNameAuth.auth_num || '0'  // 添加认证次数返回
    });
  } catch (error) {
    console.error('获取用户认证状态异常:', error);
    return handleError(error);
  }
}

/************ 云函数入口 ************/
exports.main = async (event, context) => {
  // 获取客户端环境信息与用户凭证
  const { CLIENTIP, PLATFORM, APPID } = context;
  const { action, params = {}, uniIdToken } = event;
  
  // 获取用户ID
  let userId = '';
  
  // 验证用户登录状态
  if (!uniIdToken) {
    return createErrorResponse(ERROR_CODES.TOKEN_EXPIRED, '用户未登录');
  }
  
  try {
    // 创建uniId实例，使用正确的配置
    const uniID = uniIdCommon.createInstance({
      context,
      config: {
        ...userConfig,
        provider: {
          alipay: {
            appId: APPID
          }
        }
      }
    });
    
    const checkTokenRes = await uniID.checkToken(uniIdToken);
    if (checkTokenRes.code !== 0) {
      return createErrorResponse(ERROR_CODES.TOKEN_EXPIRED, '用户未登录或登录状态失效');
    }
    
    userId = checkTokenRes.uid;
  } catch (error) {
    console.error('校验token异常:', error);
    return createErrorResponse(ERROR_CODES.TOKEN_EXPIRED, '用户未登录或登录状态失效');
  }
  
  // 根据不同的action执行相应的操作
  try {
    switch (action) {
      // 获取用户认证状态
      case 'getCertifyStatus':
        return await getUserCertifyStatus(userId);
        
      // 初始化认证
      case 'initializeCertify': {
        const { realName, identityCard } = params;
        if (!realName || !identityCard) {
          return createErrorResponse(ERROR_CODES.PARAM_ERROR, '姓名和身份证号不能为空');
        }
        return await initializeCertify(userId, realName, identityCard);
      }
        
      // 获取认证URL
      case 'getCertifyUrl': {
        const { certifyId } = params;
        if (!certifyId) {
          return createErrorResponse(ERROR_CODES.PARAM_ERROR, '认证ID不能为空');
        }
        return await getCertifyUrl(userId, certifyId);
      }
        
      // 查询认证结果
      case 'queryCertifyResult': {
        const { certifyId } = params;
        if (!certifyId) {
          return createErrorResponse(ERROR_CODES.PARAM_ERROR, '认证ID不能为空');
        }
        return await queryCertifyResult(userId, certifyId);
      }
        
      // 未知操作
      default:
        return createErrorResponse(ERROR_CODES.PARAM_ERROR, `未知的操作: ${action}`);
    }
  } catch (error) {
    console.error(`执行${action}操作异常:`, error);
    return handleError(error);
  }
}; 