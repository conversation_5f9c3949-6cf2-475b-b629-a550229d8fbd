module.exports = {
  userConfig: {
    tokenSecret: 'uyiGameToken', // 生成token所用的密钥
    requestAuthSecret: 'uyiGameUrl', // URL化请求鉴权签名密钥
    // tokenExpiresIn: 120, // token过期时间（秒）
    // tokenExpiresThreshold: 60, // token过期预警时间（秒）
    tokenExpiresIn: 259200, // token过期时间（秒）
    tokenExpiresThreshold: 86400, // token过期预警时间（秒）
    passwordErrorLimit: 6, // 密码错误重试次数
    passwordErrorRetryTime: 3600, // 密码错误重试次数超限之后的冻结时间
  },
  alipayConfig: {
    // 应用ID
    appId: '2021005123608796',
    alipayApiKey: 'PODyMxQUfuRI8tzH/6xE9w==', // AES加密
    // 应用私钥
    privateKey: 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCt0p4huwGPWgjeoXc452jgudRkiJznVn+U0eRr0t4G+tgJJ7WGOtc/sr8dmYYW6D8fhIB9p9sN+/Np62J1FDvsoPbR97kkZ8VJpDzYP5KhPoOWcXtFPCyGExUFKJdYz1It2+LSBMS8tNRb/5i36aSwtNScMZ1faRzGuqCZK9wUbVBiS2Rgs7Ua+RYzD9J3kJc8npmO/r7i1eOpQ/kveqSBXvsUVnuLhQxPLrQuBfwUj4Gce+fLfr+PyL2OtiM4yxvyEN669TIFmpxrpWb5qgrm6RZEvSZl2GnaZhYGCg/XmeMMjCLFqjqElS1ervuQq5fIsWvkBODWZzP5vPIqY4y9AgMBAAECggEBAJiGXvHUKZtDctMANiB9lp7TEVciNSQd15eb0eb3QpI3XIg0Cun9CdHAJElx5KKHKpSabTFVsDcGJWKFBIEdvzN1Tv+RcylJ0Wk8L5PFvQG2XDsP2w42rg78AY7kPmHUkhThhJerTYMU3d7YWobPAw93rbRdKO2Gy5RqQPAG93gHvK6xnyFdgFzsNYTLK7/WsfpKhKIE7Q6uwWeSFb+ZfTS97s2rmqgKcARsGich0aeWVwGlJ0BWuxQoA2lL0DjC4yumTDkzONP+absAKHyGP8/K+w7hFn7arjnOcJzUBHpdgq6MZFATDeeo2Cdy8tAI0XIWU3JO9umdGJ06ZJHFbtUCgYEA4zDWeOcH1hjne+0DGKj9p/p3rvHaIP3STHkcU//YBLMv9RCHKDv5Tin4uRDQWaAeakmYKPoTJNykrvK2J7CZSot8rhaC1NuV+5TYHv/eAOe8expvXiA0EiWB17VdTZq/wurvaeoDGV/EoaIvpIYWLXbAJ4G+GWPdTQV+4Z4pdOcCgYEAw91S79VfRw3fN5OUqbJJS+kIV843IFaKVi3x1kMkKKAStEjhK6qDuaColesmqinL/IC1pjGiLxPetP8kJ3nYtJwPBkK307paLlPxNCLh33BhUrBYpabmguToirVZovIK3Jz2Tq88vpTdAuDkvPUDP5AFf1amxMYb79sbxk1+mLsCgYEAuXBijxrcXZfEUnXZ4iYFrTeWuxgzmFw1bDAqvDGHLAbqbRiJiYCydGdz2fLsbUtxqAXcCg5c0+i5DweTQQA3jw4dvacoMP/vAwOyCFOG7Kw6Py0LnAOjHPC6c8xYlQBr53iF5Kia0PpHcDsGsj60GXXlUBnphaXqu3Rx5UyIwB8CgYAsQ6mJsCQPL9BhhAXV8Afmkuyiw2Tr9wLehRWhgZz7XWm8urfAoZGRcwCGmRUs4bNLBl+HBrKLhycOqpamIUBbWOWAWbWnsPbOf4QEb1myoHguMtJl5Xk03by8FE4EqETSJNr5X1sQCx6DlwW6uniYddj4NUINOGR56X+2ai8RHwKBgQDaST5Qpad2tA0NoLdDvODLpuKghb+P+Cl3hmOcH1m0BOE3k5UgkXqFAJSBVksRJJwrfhhlcZPRJNwC1SBhk49U1bDuDvNngpy+lYxKMpHm2Off79JrTRCVnIJYXAq4P2ldj9U6uU+9riDQ1iveDUSJdKQsYq5RWFxkLk3K19KVSw==',
    // 支付宝公钥
    alipayPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl/ssNeBbKh3O855boBDcsv2b3EqVavmbm9Ljtn+2A8IOvdZaVRbaX2LrMT7ZuqpmD2fDcbw3pjJuMJYeX0LxORecedO6JOUmAefEV0OKdQNueIy01wyGDhjDLLIDrTfL31z1Lr1dCQFoxbur62+IqBQajSdo8hShrbcwsrTzQTWy2nUKa1G7v++lO4qWz2rNJNlBHT91+WkTLYkgntDQnHdf+H14cgt9P28zt6+b7jDbuGEwWZXc5gadRBHSWz8QzrjQP93F6So29XMZLnZe0scJbFWvsML/IlL8P9F+U0p6pJbd6DZcqlRIZ1YNsT5Y7VuffDnl2RQudKqNTTUolQIDAQAB',
    // 网关
    gateway: 'https://openapi.alipay.com/gateway.do',
    // 签名类型
    signType: 'RSA2',
    // 是否使用证书模式
    keyType: 'PKCS8',
    // 网页授权回调地址
    oauth_redirect_url: '',
    // 人脸保存策略 reserve(保存) never(不保存)
    reserve_strategy: 'reserve'
  }
}; 