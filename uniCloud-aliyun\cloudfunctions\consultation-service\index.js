'use strict';
const https = require('https');

exports.main = async (event, context) => {
	// 参数校验
	if (!event.phone) {
		return {
			code: -1,
			message: '联系方式不能为空'
		};
	}
	
	try {
		// 1. 发送消息到Server酱API
		const scKey = 'SCT1088TztRBxqH4dpE1BmWQYVC3Cf29'; // Server酱密钥
		const title = '新的合租咨询';
		const content = `联系方式: ${event.phone}\n提交时间: ${new Date(new Date().getTime() + 8 * 60 * 60 * 1000).toLocaleString()}`;
		
		// 发送通知
		await sendNotification(scKey, title, content);
		
		// 2. 存储到数据库
		const db = uniCloud.database();
		const consultationCollection = db.collection('consultations');
		
		// 创建记录
		await consultationCollection.add({
			phone: event.phone,
			createTime: new Date(),
			status: 'pending', // 状态：待处理
			source: 'co-renting', // 来源页面
			platform: event.platform || 'unknown' // 平台信息
		});
		
		return {
			code: 0,
			message: '提交成功'
		};
	} catch (error) {
		console.error('处理咨询请求失败:', error);
		return {
			code: -2,
			message: '服务器处理失败，请稍后重试',
			error: error.message
		};
	}
};

// 发送Server酱通知的函数
function sendNotification(key, title, content) {
	return new Promise((resolve, reject) => {
		if (!key) {
			console.warn('未配置Server酱密钥，跳过通知发送');
			resolve();
			return;
		}
		
		// 构建请求URL
		const apiUrl = `https://sctapi.ftqq.com/${key}.send?title=${encodeURIComponent(title)}&desp=${encodeURIComponent(content)}`;
		const parsedUrl = new URL(apiUrl);
		
		const options = {
			hostname: parsedUrl.hostname,
			path: parsedUrl.pathname + parsedUrl.search,
			method: 'GET'
		};
		
		const req = https.request(options, (res) => {
			let data = '';
			
			res.on('data', (chunk) => {
				data += chunk;
			});
			
			res.on('end', () => {
				try {
					const result = JSON.parse(data);
					if (result.code === 0) {
						resolve(result);
					} else {
						reject(new Error(`Server酱API返回错误: ${result.message || '未知错误'}`));
					}
				} catch (err) {
					reject(new Error('解析Server酱API响应失败: ' + err.message));
				}
			});
		});
		
		req.on('error', (err) => {
			reject(new Error('发送通知请求失败: ' + err.message));
		});
		
		req.end();
	});
} 