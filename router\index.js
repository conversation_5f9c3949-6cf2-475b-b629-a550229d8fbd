import {RouterMount,createRouter} from 'uni-simple-router';

const router = createRouter({
	platform: process.env.VUE_APP_PLATFORM,  
	routes: [...ROUTES]
});

// const routeWhitelist = [
//     '/pages/index',
// ] // 路由白名单

//全局路由前置守卫
router.beforeEach((to, from, next) => {
    // if (!routeWhitelist.includes(to.path) && !uni.getStorageSync('token')) {
    //     uni.showModal({
    //         title: '温馨提示',
    //         content: '是否前去登录',
    //         success: res => {
    //             if (res.confirm) {
    //                 next('/mainPackage/login/index')
    //             } else if (res.cancel) {
    //                 console.log('用户点击取消')
    //             }
    //         }
    //     })
    //     next(false)
    //     return
    // }
    next()
});

// 全局路由后置守卫
router.afterEach((to, from) => {
    console.log('跳转结束')
})

export {
	router,
	RouterMount
}