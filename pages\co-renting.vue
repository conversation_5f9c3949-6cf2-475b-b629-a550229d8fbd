<template>
    <view class="container" :class="animationActive ? 'animation-active' : ''">
        <view class="header">
            <u-status-bar bgColor="transparent"></u-status-bar>
            <view class="header-content">
                <!-- #ifndef MP-ALIPAY -->
                <view class="back-button" @click="goBack">
                    <image src="/static/images/back.svg" mode="aspectFit"></image>
                </view>
                <!-- #endif -->
                <view class="header-text">
                    <text class="page-title">闲置账号赚钱</text>
                </view>
            </view>
        </view>

        <view class="main-content">
            <!-- 英雄区域 -->
            <view class="hero-section fade-in">
                <view class="hero-title">绝地求生账号托管</view>
                <view class="hero-subtitle">躺着也能赚收益</view>
                <view class="hero-description">
                    无需您操作，专业团队24小时运营，账号安全有保障
                </view>
                <view class="hero-stats">
                    <view class="stat-item">
                        <text class="stat-value">30-200</text>
                        <text class="stat-label">元/天</text>
                    </view>
                    <view class="stat-divider"></view>
                    <view class="stat-item">
                        <text class="stat-value">200+</text>
                        <text class="stat-label">成功合作</text>
                    </view>
                    <view class="stat-divider"></view>
                    <view class="stat-item">
                        <text class="stat-value">100%</text>
                        <text class="stat-label">安全保障</text>
                    </view>
                </view>
            </view>
            
            <!-- 优势展示 -->
            <view class="benefits-section slide-in">
                <view class="benefit-item">
                    <view class="benefit-icon">
                        <image src="/static/images/money.svg" mode="aspectFit"></image>
                    </view>
                    <view class="benefit-content">
                        <text class="benefit-title">稳定收益</text>
                        <text class="benefit-desc">根据账号不同情况，每天稳定收入30-200元，多种结算方式</text>
                    </view>
                </view>
                <view class="benefit-item">
                    <view class="benefit-icon">
                        <image src="/static/images/security.svg" mode="aspectFit"></image>
                    </view>
                    <view class="benefit-content">
                        <text class="benefit-title">安全无忧</text>
                        <text class="benefit-desc">专业团队运营，严格遵守游戏规则，确保账号安全</text>
                    </view>
                </view>
                <view class="benefit-item">
                    <view class="benefit-icon">
                        <image src="/static/images/time.svg" mode="aspectFit"></image>
                    </view>
                    <view class="benefit-content">
                        <text class="benefit-title">灵活周期</text>
                        <text class="benefit-desc">订单透明，随时托管，随时取回，无长期合约，操作便捷</text>
                    </view>
                </view>
            </view>
            
            <!-- 表单区域 -->
            <view class="form-section fade-in-delay">
                <view class="section-title">
                    <text class="title-text">立即参与</text>
                    <text class="title-desc">留下联系方式，获取专属收益方案</text>
                </view>
                
                <view class="form-container">
                    <view class="input-group">
                        <view class="input-icon">
                            <image class="input-icon-img" src="/static/images/contact.svg" mode="aspectFit"></image>
                        </view>
                        <u-input
                            v-model="formData.phone"
                            type="text"
                            placeholder="请输入您的微信或手机号"
                            border="none"
                            fontSize="30rpx"
                            color="#ffffff"
                            inputAlign="left"
                            placeholderStyle="color: rgba(255, 255, 255, 0.3)"
                            maxlength="20"
                        ></u-input>
                    </view>
                    
                    <button
                        class="submit-btn"
                        hover-class="submit-btn-hover"
                        :loading="isLoading"
                        @click="submitForm"
                    >
                        立即咨询
                    </button>
                </view>
            </view>

            <!-- 合作说明 -->
            <view class="notice-box fade-in-delay">
                <view class="notice-title">
                    <image class="title-icon" src="/static/images/info.svg" mode="aspectFit"></image>
                    <text>合作保障</text>
                </view>
                <view class="notice-list">
                    <view class="notice-item">我们提供专业的账号托管服务，7年运营经验</view>
                    <view class="notice-item">严格遵守游戏规则，不使用任何作弊手段</view>
                    <view class="notice-item">提交信息后，专属客服第一时间与您联系</view>
                    <view class="notice-item">合作期间可随时查询账号状态，透明安全</view>
                </view>
                <view class="security-badges">
                    <view class="badge">
                        <view class="badge-icon secure"></view>
                        <text>安全保障</text>
                    </view>
                    <view class="badge">
                        <view class="badge-icon privacy"></view>
                        <text>隐私保护</text>
                    </view>
                    <view class="badge">
                        <view class="badge-icon support"></view>
                        <text>7×24客服</text>
                    </view>
                </view>
            </view>
        </view>
        <u-safe-bottom></u-safe-bottom>
    </view>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
    name: 'CoRentingPage',
    
    data() {
        return {
            formData: {
                phone: '',
            },
            isLoading: false,
            // 动画相关
            animationActive: false
        }
    },
    
    mounted() {
        // 启动进入动画
        setTimeout(() => {
            this.animationActive = true;
        }, 300);
    },
    
    computed: {
        // 使用mapGetters从vuex中映射计算属性
        ...mapGetters([
            'isLoggedIn'
        ]),
        
        // 登录状态语义化别名
        isLogin() {
            return this.isLoggedIn
        }
    },
    
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },
        
        // 表单验证
        validateForm() {
            if (!this.formData.phone.trim()) {
                this.showToast('请输入您的手机号');
                return false;
            }
            
            // 验证手机号格式 (同时支持手机号和微信号格式)
            const phoneRegex = /^1[3-9]\d{9}$/;
            const wxRegex = /^[a-zA-Z][\w-]{5,19}$/;
            
            if (!phoneRegex.test(this.formData.phone) && !wxRegex.test(this.formData.phone)) {
                this.showToast('请输入正确的手机号或微信号');
                return false;
            }
            
            return true;
        },
        
        // 提交表单
        async submitForm() {
            if (!this.validateForm()) {
                return;
            }
            
            // 检查登录状态
            if (!this.isLogin) {
                uni.showModal({
                    title: '提示',
                    content: '请先登录后再提交',
                    confirmText: '去登录',
                    success: (res) => {
                        if (res.confirm) {
                            this.navigateToLogin();
                        }
                    }
                });
                return;
            }
            
            this.isLoading = true;
            
            try {
                // 调用云函数提交表单数据
                const result = await uniCloud.callFunction({
                    name: 'consultation-service',
                    data: {
                        phone: this.formData.phone,
                        platform: uni.getSystemInfoSync().platform
                    }
                });
                
                this.isLoading = false;
                
                if (result.result && result.result.code === 0) {
                    // 显示提交成功提示
                    uni.showModal({
                        title: '提交成功',
                        content: '您的信息已提交，我们将尽快与您联系',
                        showCancel: false,
                        confirmText: '知道了',
                        success: () => {
                            // 重置表单
                            this.formData = {
                                phone: ''
                            };
                        }
                    });
                } else {
                    this.showToast(result.result.message || '提交失败，请稍后重试');
                }
                
            } catch (error) {
                console.error('提交表单失败:', error);
                this.showToast('提交失败，请稍后重试');
                this.isLoading = false;
            }
        },
        
        // 显示提示信息
        showToast(message, icon = 'none', duration = 1500) {
            uni.showToast({
                title: message,
                icon: icon,
                duration: duration
            });
        },
        
        // 导航到登录页面
        navigateToLogin() {
            // 保存当前页面路径，以便登录后返回
            const currentPage = getCurrentPages()[getCurrentPages().length - 1].route;
            const returnUrl = encodeURIComponent(`/${currentPage}`);
            
            // 跳转到首页（首页有登录功能）
            uni.navigateTo({
                url: `/pages/index?returnUrl=${returnUrl}`
            });
        }
    }
}
</script>

<style lang="scss">
/* 变量使用与主页面保持一致 */
page {
    --primary-color: #1b2838;
    --primary-dark: #171a21;
    --accent-color: #66c0f4;
    --accent-hover: #1999ff;
    --background: #171a21;
    --text-primary: #ffffff;
    --text-secondary: #8f98a0;
    --success-color: #07c160;
    --warning-color: #ff9800;
    color: var(--text-primary);
    background-color: var(--background);
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20rpx); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { opacity: 0; transform: translateX(-30rpx); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes shine {
    to { left: 100%; }
}

.animation-active .fade-in {
    animation: fadeIn 0.8s ease-out forwards;
}

.animation-active .slide-in {
    animation: slideIn 0.8s ease-out forwards;
    animation-delay: 0.3s;
    opacity: 0;
    animation-fill-mode: forwards;
}

.animation-active .fade-in-delay {
    animation: fadeIn 0.8s ease-out forwards;
    animation-delay: 0.5s;
    opacity: 0;
    animation-fill-mode: forwards;
}

.animation-active .slide-in-delay {
    animation: slideIn 0.8s ease-out forwards;
    animation-delay: 0.7s;
    opacity: 0;
    animation-fill-mode: forwards;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: var(--background);
    background-image: linear-gradient(to bottom,
            rgba(27, 40, 56, 0.8),
            rgba(23, 26, 33, 1));
}

.header {
    background: linear-gradient(180deg, #2a475e 0%, var(--primary-color) 100%);
    border-bottom: 1px solid rgba(102, 192, 244, 0.2);
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
    width: 100%;
    flex-shrink: 0;
}

.header-content {
    height: 80rpx;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 32rpx;
    box-sizing: border-box;
}

.back-button {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
        width: 36rpx;
        height: 36rpx;
    }
}

.header-text {
    flex: 1;
    text-align: center;
    // #ifndef MP-ALIPAY
    margin-left: -60rpx;
    // #endif
}

.page-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: 1rpx;
}

.main-content {
    flex: 1;
    padding: 40rpx 40rpx 40rpx 40rpx;
    width: 100%;
    box-sizing: border-box;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}

/* 英雄区域样式 */
.hero-section {
    background: linear-gradient(135deg, rgba(102, 192, 244, 0.15), rgba(25, 153, 255, 0.1));
    border-radius: 16rpx;
    padding: 60rpx 40rpx;
    margin-bottom: 50rpx;
    text-align: center;
    border: 1px solid rgba(102, 192, 244, 0.3);
    position: relative;
    overflow: hidden;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    }
    
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 200rpx;
        background: linear-gradient(to top, rgba(27, 40, 56, 0.3), transparent);
        z-index: 1;
    }
}

.hero-title {
    font-size: 48rpx;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 16rpx;
    letter-spacing: 2rpx;
    text-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
    font-size: 36rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 30rpx;
    text-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.3);
}

.hero-description {
    font-size: 28rpx;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 80%;
    margin: 0 auto;
}

.hero-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 40rpx;
    padding: 20rpx;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 12rpx;
    position: relative;
    z-index: 2;
}

.stat-item {
    text-align: center;
    padding: 0 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-value {
    font-size: 40rpx;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 8rpx;
    text-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 24rpx;
    color: var(--text-secondary);
}

.stat-divider {
    width: 2rpx;
    height: 60rpx;
    background-color: rgba(255, 255, 255, 0.2);
}

/* 优势展示区域 */
.benefits-section {
    background: linear-gradient(135deg, rgba(102, 192, 244, 0.08), rgba(27, 40, 56, 0.08));
    border-radius: 16rpx;
    padding: 44rpx;
    border: 1px solid rgba(102, 192, 244, 0.15);
    margin-bottom: 50rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(to bottom, var(--accent-color), #2a475e);
        opacity: 0.8;
    }
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 1px solid rgba(102, 192, 244, 0.1);
    
    &:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }
}

.benefit-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 30rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.benefit-content {
    flex: 1;
}

.benefit-title {
    font-size: 32rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-right: 8rpx;
    margin-bottom: 8rpx;
}

.benefit-desc {
    font-size: 26rpx;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 表单区域 */
.form-section {
    margin-bottom: 50rpx;
}

.section-title {
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column;
}

.title-text {
    font-size: 36rpx;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10rpx;
    display: flex;
    align-items: center;
    
    &::before {
        content: "";
        width: 8rpx;
        height: 36rpx;
        background: var(--accent-color);
        border-radius: 4rpx;
        margin-right: 20rpx;
        flex-shrink: 0;
    }
}

.title-desc {
    font-size: 26rpx;
    color: var(--text-secondary);
    margin-left: 28rpx;
}

.form-container {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 16rpx;
    padding: 40rpx;
    border: 1px solid rgba(102, 192, 244, 0.15);
    position: relative;
    overflow: hidden;
    box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2rpx;
        background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    }
}

.input-group {
    margin-bottom: 40rpx;
    position: relative;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(102, 192, 244, 0.2);
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    padding: 0 20rpx;
    
    &::before,
    &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg,
                transparent 0%,
                var(--accent-color) 50%,
                transparent 100%);
        opacity: 0.3;
    }

    &::before {
        top: 0;
    }

    &::after {
        bottom: 0;
        opacity: 0.2;
    }

    /* UniApp 输入框样式覆盖 */
    .u-input {
        height: 90rpx !important;
        .u-input__content__field-wrapper__field  {
            background: transparent !important;
        }
    }
}

.input-icon {
    width: 30rpx;
    height: 30rpx;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .input-icon-img {
        width: 100%;
        height: 100%;
    }
}

.submit-btn {
    border: none;
    border-radius: 8rpx;
    background: linear-gradient(to right, var(--success-color), #05a350);
    height: 90rpx;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 700;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5rpx 15rpx rgba(7, 193, 96, 0.3);
    
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
        );
        animation: shine 2s infinite;
    }
}

.submit-btn-hover {
    background: linear-gradient(to right, #05a350, #048a44);
    transform: translateY(2rpx);
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    opacity: 0.9;
    transition: all 0.2s ease;
}

/* 合作说明区域 */
.notice-box {
    background: linear-gradient(135deg, rgba(102, 192, 244, 0.08), rgba(27, 40, 56, 0.08));
    border-radius: 16rpx;
    padding: 44rpx;
    border: 1px solid rgba(102, 192, 244, 0.15);
    margin-top: 40rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(to bottom, var(--accent-color), #2a475e);
        opacity: 0.8;
    }
}

.notice-title {
    color: var(--accent-color);
    font-size: 32rpx;
    font-weight: 600;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.title-icon {
    width: 32rpx;
    height: 32rpx;
    flex-shrink: 0;
}

.notice-list {
    list-style: none;
    margin: 0;
    padding: 0;
    margin-bottom: 30rpx;
}

.notice-item {
    color: rgba(255, 255, 255, 0.85);
    font-size: 26rpx;
    line-height: 1.6;
    margin-bottom: 16rpx;
    padding-left: 40rpx;
    position: relative;
    display: flex;
    align-items: flex-start;

    &::before {
        content: "•";
        position: absolute;
        left: 16rpx;
        color: var(--accent-color);
        opacity: 0.8;
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.security-badges {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 30rpx;
    padding-top: 30rpx;
    border-top: 1px dashed rgba(102, 192, 244, 0.2);
    font-size: 30rpx;
}

.badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx 20rpx;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8rpx;
    border: 1px solid rgba(102, 192, 244, 0.1);
}

.badge-icon {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 10rpx;
    opacity: 0.8;
}

.badge-icon.secure {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%2366c0f4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>') no-repeat center;
    background-size: contain;
}

.badge-icon.privacy {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%2366c0f4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>') no-repeat center;
    background-size: contain;
}

.badge-icon.support {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%2366c0f4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>') no-repeat center;
    background-size: contain;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
    .hero-title {
        font-size: 40rpx;
    }
    
    .hero-subtitle {
        font-size: 32rpx;
    }
    
    .stat-value {
        font-size: 36rpx;
    }
    
    .main-content {
        padding: 30rpx 30rpx 30rpx 30rpx;
    }
    
    .form-container {
        padding: 30rpx;
    }
    
    .benefits-section, .notice-box {
        padding: 30rpx;
    }
}

@media screen and (min-width: 768px) {
    .hero-title {
        font-size: 56rpx;
    }
    
    .hero-subtitle {
        font-size: 40rpx;
    }
    
    .main-content {
        padding: 60rpx 60rpx 60rpx 60rpx;
    }
    
    .hero-section {
        padding: 80rpx 60rpx;
    }
    
    .form-container {
        padding: 50rpx;
    }
}
</style> 