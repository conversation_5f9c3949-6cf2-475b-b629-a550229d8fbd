{
	"easycom": {
		"^u-(.*)": "@/uni_modules/uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index",
			"style": {
				"navigationBarTitleText": "",
				"disableScroll": true,
				"navigationStyle": "custom",
				"mp-alipay": {
					"transparentTitle": "always",
					"titlePenetrate": "YES",
					"allowsBounceVertical": "NO"
				}
			}
		},
		{
			"path": "pages/identity-verification",
			"style": {
				"navigationBarTitleText": "",
				"disableScroll": true,
				"navigationStyle": "custom",
				"mp-alipay": {
					"transparentTitle": "always",
					"titlePenetrate": "YES",
					"allowsBounceVertical": "NO"
				}
			}
		},
		{
			"path": "pages/co-renting",
			"style": {
				"navigationBarTitleText": "",
				"disableScroll": true,
				"navigationStyle": "custom",
				"mp-alipay": {
					"transparentTitle": "always",
					"titlePenetrate": "YES",
					"allowsBounceVertical": "NO"
				}
			}
		}
	],
	"subPackages": [
		{
			"root": "mainPackage",
			"pages": [
				
			]
		},
		{
			"root": "commonPackage",
			"pages": []
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "优易电玩",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F2F4F9"
	},
	"condition": { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "test", //模式名称
			"path": "mainPackage/login/index", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}
